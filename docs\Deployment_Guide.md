# AptiBot.com - Production Deployment Guide

## 🚀 **Pre-Deployment Checklist**

### **1. Environment Configuration**
- [ ] Update `.env.production` with production values
- [ ] Set real Google Analytics tracking ID
- [ ] Configure production API endpoints
- [ ] Verify all environment variables

### **2. Build Optimization**
- [ ] Run `npm run build` to create production build
- [ ] Verify bundle size and optimization
- [ ] Test production build locally with `npm run preview`
- [ ] Ensure all assets are properly optimized

### **3. SEO & Analytics Setup**
- [ ] Replace placeholder GA tracking ID with real one
- [ ] Verify sitemap.xml is accessible
- [ ] Test robots.txt configuration
- [ ] Confirm all meta tags are correct

---

## 🌐 **Deployment Options**

### **Option 1: Vercel (Recommended)**

**Why Vercel:**
- Optimized for React/Vite applications
- Automatic HTTPS and CDN
- Easy custom domain setup
- Excellent performance out of the box

**Steps:**
1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel --prod`
4. Configure custom domain in Vercel dashboard

**Vercel Configuration (vercel.json):**
```json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### **Option 2: Netlify**

**Steps:**
1. Connect GitHub repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Configure environment variables
5. Set up custom domain

**Netlify Configuration (_redirects):**
```
/*    /index.html   200
```

### **Option 3: AWS S3 + CloudFront**

**For enterprise-level deployment:**
1. Create S3 bucket for static hosting
2. Configure CloudFront distribution
3. Set up Route 53 for DNS
4. Configure SSL certificate

---

## 🔧 **Production Environment Variables**

Create `.env.production`:
```env
# Site Configuration
VITE_SITE_URL=https://aptibot.com
VITE_API_BASE_URL=https://api.aptibot.com

# Analytics Configuration
VITE_GA_TRACKING_ID=G-REAL-TRACKING-ID
VITE_ENABLE_ANALYTICS_IN_DEV=false
VITE_PERFORMANCE_MONITORING=true

# Contact Form
VITE_CONTACT_FORM_ENDPOINT=https://api.aptibot.com/contact

# Feature Flags
VITE_ENABLE_3D_SCENES=true
VITE_ENABLE_ANIMATIONS=true
```

---

## 📊 **Post-Deployment Verification**

### **1. Functionality Testing**
- [ ] All pages load correctly
- [ ] Navigation works properly
- [ ] Contact form submits successfully
- [ ] 3D scenes render properly
- [ ] Mobile responsiveness verified

### **2. Performance Testing**
- [ ] Run Lighthouse audit (aim for 90+ scores)
- [ ] Test Core Web Vitals
- [ ] Verify loading speeds globally
- [ ] Check mobile performance

### **3. SEO Verification**
- [ ] Google Search Console setup
- [ ] Submit sitemap to search engines
- [ ] Verify meta tags in browser
- [ ] Test social media sharing

### **4. Analytics Verification**
- [ ] Google Analytics receiving data
- [ ] Event tracking working
- [ ] Goal conversions set up
- [ ] Real-time data flowing

---

## 🔒 **Security Configuration**

### **HTTPS Setup**
- [ ] SSL certificate installed
- [ ] HTTP to HTTPS redirects configured
- [ ] HSTS headers enabled
- [ ] Mixed content warnings resolved

### **Security Headers**
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:;
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
```

---

## 📈 **Monitoring & Maintenance**

### **1. Performance Monitoring**
- Set up Google PageSpeed Insights monitoring
- Configure Core Web Vitals alerts
- Monitor bundle size changes
- Track loading performance metrics

### **2. Error Tracking**
- Implement error boundary components
- Set up error logging service (Sentry)
- Monitor JavaScript errors
- Track failed form submissions

### **3. Analytics Monitoring**
- Set up Google Analytics goals
- Monitor conversion rates
- Track user engagement metrics
- Analyze traffic sources

### **4. SEO Monitoring**
- Monitor search rankings
- Track organic traffic growth
- Check for crawl errors
- Monitor page indexing status

---

## 🔄 **Continuous Deployment**

### **GitHub Actions Workflow**
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    - name: Install dependencies
      run: npm ci
    - name: Build
      run: npm run build
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

---

## 📞 **Support & Maintenance**

### **Regular Maintenance Tasks**
- [ ] Update dependencies monthly
- [ ] Review and update content quarterly
- [ ] Performance audits quarterly
- [ ] Security updates as needed
- [ ] Analytics review monthly

### **Emergency Procedures**
- [ ] Rollback procedure documented
- [ ] Emergency contact information
- [ ] Backup and recovery plan
- [ ] Incident response protocol

---

## ✅ **Final Deployment Checklist**

**Before Going Live:**
- [ ] All tests passing
- [ ] Production build successful
- [ ] Environment variables configured
- [ ] Domain and SSL configured
- [ ] Analytics tracking verified
- [ ] Performance benchmarks met
- [ ] Security headers configured
- [ ] Monitoring systems active

**After Going Live:**
- [ ] Verify all functionality
- [ ] Submit to search engines
- [ ] Monitor for 24 hours
- [ ] Update documentation
- [ ] Notify stakeholders

**🎉 Ready for Production Launch!**
