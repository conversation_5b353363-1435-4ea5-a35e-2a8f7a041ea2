import { lazy, ComponentType } from 'react';

interface LazyComponentOptions {
  fallback?: ComponentType;
  retryCount?: number;
  retryDelay?: number;
  preload?: boolean;
}

/**
 * Enhanced lazy loading with retry logic and preloading
 */
export const createLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyComponentOptions = {}
): ComponentType<any> => {
  const { retryCount = 3, retryDelay = 1000 } = options;

  const retryImport = async (attempt = 1): Promise<{ default: T }> => {
    try {
      return await importFn();
    } catch (error) {
      if (attempt < retryCount) {
        console.warn(`Failed to load component, retrying... (${attempt}/${retryCount})`);
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        return retryImport(attempt + 1);
      }
      throw error;
    }
  };

  return lazy(retryImport);
};

/**
 * Preload a lazy component
 */
export const preloadComponent = (importFn: () => Promise<any>): void => {
  const componentImport = importFn();
  
  // Store the promise to avoid duplicate requests
  if (typeof window !== 'undefined') {
    (window as any).__componentCache = (window as any).__componentCache || new Map();
    (window as any).__componentCache.set(importFn.toString(), componentImport);
  }
};

/**
 * Route-based code splitting helper
 */
export const createRouteComponent = (
  importFn: () => Promise<{ default: ComponentType<any> }>,
  preloadCondition?: () => boolean
) => {
  // Preload if condition is met
  if (preloadCondition && preloadCondition()) {
    preloadComponent(importFn);
  }

  return createLazyComponent(importFn, {
    retryCount: 3,
    retryDelay: 1000,
  });
};

/**
 * Feature-based code splitting
 */
export const createFeatureComponent = (
  importFn: () => Promise<{ default: ComponentType<any> }>,
  featureFlag?: boolean
) => {
  if (featureFlag === false) {
    // Return a placeholder component if feature is disabled
    return () => null;
  }

  return createLazyComponent(importFn);
};

/**
 * Intersection Observer based preloading
 */
export const preloadOnIntersection = (
  element: Element,
  importFn: () => Promise<any>,
  options: IntersectionObserverInit = {}
): void => {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return;
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          preloadComponent(importFn);
          observer.unobserve(element);
        }
      });
    },
    {
      rootMargin: '100px',
      threshold: 0.1,
      ...options,
    }
  );

  observer.observe(element);
};

/**
 * Hover-based preloading
 */
export const preloadOnHover = (
  element: Element,
  importFn: () => Promise<any>
): void => {
  let hasPreloaded = false;

  const handleMouseEnter = () => {
    if (!hasPreloaded) {
      preloadComponent(importFn);
      hasPreloaded = true;
    }
  };

  element.addEventListener('mouseenter', handleMouseEnter, { once: true });
  element.addEventListener('focus', handleMouseEnter, { once: true });
};

/**
 * Network-aware preloading
 */
export const preloadOnGoodConnection = (
  importFn: () => Promise<any>
): void => {
  if (typeof window === 'undefined') return;

  const connection = (navigator as any).connection;
  
  if (connection) {
    // Only preload on fast connections
    const isFastConnection = 
      connection.effectiveType === '4g' && 
      connection.downlink > 1.5 && 
      !connection.saveData;

    if (isFastConnection) {
      preloadComponent(importFn);
    }
  } else {
    // Fallback: preload after a delay if no connection info
    setTimeout(() => preloadComponent(importFn), 2000);
  }
};

/**
 * Bundle analyzer helper
 */
export const logBundleSize = (componentName: string, importFn: () => Promise<any>): void => {
  if (process.env.NODE_ENV === 'development') {
    const startTime = performance.now();
    
    importFn().then(() => {
      const loadTime = performance.now() - startTime;
      console.log(`Component "${componentName}" loaded in ${loadTime.toFixed(2)}ms`);
    }).catch((error) => {
      console.error(`Failed to load component "${componentName}":`, error);
    });
  }
};

/**
 * Smart preloading based on user behavior
 */
export class SmartPreloader {
  private preloadedComponents = new Set<string>();
  private userInteractionCount = 0;
  private isIdle = false;

  constructor() {
    this.setupIdleDetection();
    this.setupInteractionTracking();
  }

  private setupIdleDetection(): void {
    if (typeof window === 'undefined') return;

    let idleTimer: NodeJS.Timeout;

    const resetIdleTimer = () => {
      this.isIdle = false;
      clearTimeout(idleTimer);
      idleTimer = setTimeout(() => {
        this.isIdle = true;
      }, 3000); // 3 seconds of inactivity
    };

    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      window.addEventListener(event, resetIdleTimer, { passive: true });
    });

    resetIdleTimer();
  }

  private setupInteractionTracking(): void {
    if (typeof window === 'undefined') return;

    ['click', 'keydown', 'scroll'].forEach(event => {
      window.addEventListener(event, () => {
        this.userInteractionCount++;
      }, { passive: true });
    });
  }

  preload(
    componentId: string,
    importFn: () => Promise<any>,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ): void {
    if (this.preloadedComponents.has(componentId)) {
      return;
    }

    const shouldPreload = this.shouldPreload(priority);

    if (shouldPreload) {
      this.preloadedComponents.add(componentId);
      preloadComponent(importFn);
    }
  }

  private shouldPreload(priority: 'low' | 'medium' | 'high'): boolean {
    // High priority: always preload
    if (priority === 'high') return true;

    // Medium priority: preload if user is engaged
    if (priority === 'medium') {
      return this.userInteractionCount > 2 || this.isIdle;
    }

    // Low priority: preload only when idle and user is engaged
    return priority === 'low' && this.isIdle && this.userInteractionCount > 5;
  }
}

// Global instance
export const smartPreloader = new SmartPreloader();
