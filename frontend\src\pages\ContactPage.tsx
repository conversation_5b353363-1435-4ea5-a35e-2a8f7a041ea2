import React from 'react';
import { ContactSection } from '../components/organisms';

const ContactPage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <section className="section-padding bg-gradient-to-br from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6">
              Contact Us
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              Ready to transform your business? Let's discuss how we can help you 
              achieve your goals with our full-stack solutions.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section - Reusing the existing comprehensive contact section */}
      <ContactSection />

      {/* Additional Contact Information */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Other Ways to Reach Us
              </h2>
              <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                Choose the communication method that works best for you. We're here to help 
                and respond promptly to all inquiries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Phone Support */}
              <div className="bg-white p-8 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Phone Support</h3>
                <p className="text-secondary-600 mb-4">
                  Speak directly with our team for immediate assistance and consultation.
                </p>
                <a 
                  href="tel:+15551234567" 
                  className="text-lg font-semibold text-primary-600 hover:text-primary-700"
                >
                  +****************
                </a>
                <p className="text-sm text-secondary-500 mt-2">Mon-Fri, 9AM-6PM EST</p>
              </div>

              {/* Email Support */}
              <div className="bg-white p-8 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Email Support</h3>
                <p className="text-secondary-600 mb-4">
                  Send us a detailed message and we'll respond within 24 hours.
                </p>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-lg font-semibold text-primary-600 hover:text-primary-700"
                >
                  <EMAIL>
                </a>
                <p className="text-sm text-secondary-500 mt-2">24/7 Response</p>
              </div>

              {/* Office Visit */}
              <div className="bg-white p-8 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Office Visit</h3>
                <p className="text-secondary-600 mb-4">
                  Schedule a meeting at our office for in-person consultation.
                </p>
                <address className="text-lg font-semibold text-primary-600 not-italic">
                  123 Innovation Drive<br />
                  Tech City, TC 12345
                </address>
                <p className="text-sm text-secondary-500 mt-2">By appointment only</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-secondary-600 leading-relaxed">
                Quick answers to common questions about our services and process.
              </p>
            </div>

            <div className="space-y-8">
              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                  How long does a typical project take?
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  Project timelines vary based on complexity and scope. Simple websites typically take 2-4 weeks, 
                  while complex web applications can take 2-6 months. We'll provide a detailed timeline during 
                  our initial consultation.
                </p>
              </div>

              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                  Do you provide ongoing support and maintenance?
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  Yes! We offer comprehensive support and maintenance packages to keep your applications 
                  running smoothly, secure, and up-to-date. This includes regular updates, security monitoring, 
                  and technical support.
                </p>
              </div>

              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                  What technologies do you work with?
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  We specialize in modern web technologies including React, TypeScript, Node.js, Python, 
                  and cloud platforms like AWS and Vercel. We choose the best technology stack for each 
                  project based on your specific requirements.
                </p>
              </div>

              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                  Can you help with existing projects or only new ones?
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  We work with both new projects and existing applications. Whether you need to modernize 
                  legacy systems, add new features, fix bugs, or improve performance, we can help assess 
                  your current setup and provide solutions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
