import React, { useState } from 'react';
import { GlassCard, GlassButton, GlassSection, GlassInput, SEOHead, trackEvent, trackCustomEvent } from '../components/atoms';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Track form submission start
    trackEvent('form_submit_start', 'contact', 'contact_form');

    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Form submitted:', formData);

      // Track successful form submission
      trackEvent('form_submit_success', 'contact', 'contact_form');
      trackCustomEvent('contact_form_submission', {
        has_company: !!formData.company,
        message_length: formData.message.length,
        form_completion_time: Date.now(), // In real app, track actual time
      });

      setIsSubmitting(false);

      // Reset form
      setFormData({ name: '', email: '', company: '', message: '' });

    } catch (error) {
      // Track form submission error
      trackEvent('form_submit_error', 'contact', 'contact_form');
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* SEO Optimization */}
      <SEOHead
        title="Contact Us - Get in Touch with AptiBot"
        description="Ready to transform your business? Contact AptiBot for full-stack development and automation solutions. Get in touch via phone, email, or our contact form."
        keywords="contact AptiBot, get in touch, full-stack development consultation, business automation inquiry, project discussion"
        url="https://aptibot.com/contact"
        type="website"
      />

      {/* Glassmorphism Hero Section */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-heading font-bold mb-6 text-white leading-tight">
              Contact <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">Us</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              Ready to transform your business? Let's discuss how we can help you
              achieve your goals with our full-stack solutions.
            </p>
          </div>
        </div>
      </GlassSection>

      {/* Contact Form Section with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Let's Build Something <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Amazing Together</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Ready to transform your ideas into reality? Get in touch with our team of experts
                and let's discuss how we can help you achieve your goals.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
              {/* Contact Information */}
              <GlassCard variant="primary" size="lg" hover glow>
                <h3 className="text-2xl font-semibold text-white mb-8 flex items-center space-x-2">
                  <span>📞</span>
                  <span>Get in Touch</span>
                </h3>

                <div className="space-y-6">
                  {[
                    {
                      icon: "📧",
                      title: "Email Us",
                      content: "<EMAIL>",
                      description: "Send us a message anytime",
                      link: "mailto:<EMAIL>"
                    },
                    {
                      icon: "📱",
                      title: "Call Us",
                      content: "+****************",
                      description: "Mon-Fri, 9AM-6PM EST",
                      link: "tel:+15551234567"
                    },
                    {
                      icon: "📍",
                      title: "Visit Us",
                      content: "123 Innovation Drive, Tech City, TC 12345",
                      description: "By appointment only",
                      link: "#"
                    }
                  ].map((contact, index) => (
                    <div key={index} className="flex items-start space-x-4 p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors duration-300">
                      <span className="text-2xl">{contact.icon}</span>
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-1">{contact.title}</h4>
                        <a
                          href={contact.link}
                          className="text-cyan-300 hover:text-cyan-200 transition-colors duration-300 font-medium"
                        >
                          {contact.content}
                        </a>
                        <p className="text-gray-400 text-sm mt-1">{contact.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>

              {/* Contact Form */}
              <GlassCard variant="secondary" size="lg" hover glow>
                <h3 className="text-2xl font-semibold text-white mb-8 flex items-center space-x-2">
                  <span>✉️</span>
                  <span>Send us a Message</span>
                </h3>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <GlassInput
                    label="Full Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    variant="primary"
                    required
                  />

                  <GlassInput
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email address"
                    variant="primary"
                    required
                  />

                  <GlassInput
                    label="Company (Optional)"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="Enter your company name"
                    variant="primary"
                  />

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/90">
                      Message
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell us about your project..."
                      rows={5}
                      required
                      className="w-full px-4 py-3 text-base rounded-xl text-white placeholder-white/50 transition-all duration-300 outline-none focus:ring-2 focus:ring-cyan-400/20"
                      style={{
                        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                        backdropFilter: 'blur(20px) saturate(180%)',
                        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
                      }}
                    />
                  </div>

                  <GlassButton
                    type="submit"
                    variant="accent"
                    size="lg"
                    disabled={isSubmitting}
                    loading={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </GlassButton>
                </form>
              </GlassCard>
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Additional Contact Information with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Other Ways to <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-cyan-400">Reach Us</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Choose the communication method that works best for you. We're here to help
                and respond promptly to all inquiries.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: "📞",
                  title: "Phone Support",
                  description: "Speak directly with our team for immediate assistance and consultation.",
                  contact: "+****************",
                  details: "Mon-Fri, 9AM-6PM EST",
                  link: "tel:+15551234567",
                  variant: "primary" as const
                },
                {
                  icon: "📧",
                  title: "Email Support",
                  description: "Send us a detailed message and we'll respond within 24 hours.",
                  contact: "<EMAIL>",
                  details: "24/7 Response",
                  link: "mailto:<EMAIL>",
                  variant: "secondary" as const
                },
                {
                  icon: "📍",
                  title: "Office Visit",
                  description: "Schedule a meeting at our office for in-person consultation.",
                  contact: "123 Innovation Drive, Tech City, TC 12345",
                  details: "By appointment only",
                  link: "#",
                  variant: "accent" as const
                }
              ].map((method, index) => (
                <GlassCard
                  key={index}
                  variant={method.variant}
                  size="lg"
                  hover
                  glow
                  className="group text-center"
                >
                  <div className="text-4xl mb-6 transform group-hover:scale-110 transition-transform duration-500">
                    {method.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                    {method.title}
                  </h3>
                  <p className="text-gray-300 mb-4 group-hover:text-gray-200 transition-colors duration-300">
                    {method.description}
                  </p>
                  <a
                    href={method.link}
                    className="text-lg font-semibold text-cyan-300 hover:text-cyan-200 transition-colors duration-300 block"
                  >
                    {method.contact}
                  </a>
                  <p className="text-sm text-gray-400 mt-2">{method.details}</p>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </GlassSection>

      {/* FAQ Section with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Frequently Asked <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">Questions</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed">
                Quick answers to common questions about our services and process.
              </p>
            </div>

            <div className="space-y-8">
              {[
                {
                  question: "How long does a typical project take?",
                  answer: "Project timelines vary based on complexity and scope. Simple websites typically take 2-4 weeks, while complex web applications can take 2-6 months. We'll provide a detailed timeline during our initial consultation.",
                  icon: "⏱️"
                },
                {
                  question: "Do you provide ongoing support and maintenance?",
                  answer: "Yes! We offer comprehensive support and maintenance packages to keep your applications running smoothly, secure, and up-to-date. This includes regular updates, security monitoring, and technical support.",
                  icon: "🛠️"
                },
                {
                  question: "What technologies do you work with?",
                  answer: "We specialize in modern web technologies including React, TypeScript, Node.js, Python, and cloud platforms like AWS and Vercel. We choose the best technology stack for each project based on your specific requirements.",
                  icon: "💻"
                },
                {
                  question: "Can you help with existing projects or only new ones?",
                  answer: "We work with both new projects and existing applications. Whether you need to modernize legacy systems, add new features, fix bugs, or improve performance, we can help assess your current setup and provide solutions.",
                  icon: "🔧"
                }
              ].map((faq, index) => (
                <GlassCard
                  key={index}
                  variant={index % 2 === 0 ? "primary" : "secondary"}
                  size="lg"
                  hover
                  glow
                  className="group"
                >
                  <div className="flex items-start space-x-4">
                    <span className="text-2xl transform group-hover:scale-110 transition-transform duration-500">
                      {faq.icon}
                    </span>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                        {faq.question}
                      </h3>
                      <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </GlassSection>
    </div>
  );
};

export default ContactPage;
