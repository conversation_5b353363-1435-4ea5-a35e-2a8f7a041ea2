import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../utils';
import { NavItem } from '../../types';

interface NavigationProps {
  items: NavItem[];
  className?: string;
  variant?: 'horizontal' | 'vertical';
  onItemClick?: (item: NavItem) => void;
}

const Navigation: React.FC<NavigationProps> = ({
  items,
  className,
  variant = 'horizontal',
  onItemClick,
}) => {
  const location = useLocation();

  // Check if current route is active
  const isActiveRoute = (href: string) => {
    if (href === '/' && location.pathname === '/') return true;
    if (href !== '/' && location.pathname.startsWith(href)) return true;
    return false;
  };

  const handleNavClick = (item: NavItem) => {
    if (item.external) {
      window.open(item.href, '_blank', 'noopener,noreferrer');
    }

    if (onItemClick) {
      onItemClick(item);
    }
  };

  const baseClasses = variant === 'horizontal' 
    ? 'flex items-center space-x-8' 
    : 'flex flex-col space-y-4';

  return (
    <nav className={cn(baseClasses, className)}>
      {items.map((item, index) => {
        const isActive = isActiveRoute(item.href);

        if (item.external) {
          return (
            <button
              key={index}
              onClick={() => handleNavClick(item)}
              className={cn(
                'relative text-sm font-medium transition-colors duration-200 hover:text-primary-600 focus:outline-none focus:text-primary-600',
                'text-secondary-700 hover:text-primary-600',
                variant === 'vertical' && 'text-left w-full py-2'
              )}
            >
              {item.label}
              <svg
                className="inline-block ml-1 h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                />
              </svg>
            </button>
          );
        }

        return (
          <Link
            key={index}
            to={item.href}
            onClick={() => onItemClick?.(item)}
            className={cn(
              'relative text-sm font-medium transition-colors duration-200 hover:text-primary-600 focus:outline-none focus:text-primary-600',
              isActive
                ? 'text-primary-600'
                : 'text-secondary-700 hover:text-primary-600',
              variant === 'vertical' && 'text-left w-full py-2'
            )}
          >
            {item.label}

            {/* Active indicator */}
            {isActive && variant === 'horizontal' && (
              <span className="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary-600 rounded-full" />
            )}

            {isActive && variant === 'vertical' && (
              <span className="absolute left-0 top-0 bottom-0 w-0.5 bg-primary-600 rounded-full" />
            )}
          </Link>
        );
      })}
    </nav>
  );
};

export default Navigation;
