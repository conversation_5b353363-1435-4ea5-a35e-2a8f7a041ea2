import React, { useState, useEffect } from 'react';
import { cn, scrollToElement } from '../../utils';
import { NavItem } from '../../types';

interface NavigationProps {
  items: NavItem[];
  className?: string;
  variant?: 'horizontal' | 'vertical';
  onItemClick?: (item: NavItem) => void;
}

const Navigation: React.FC<NavigationProps> = ({
  items,
  className,
  variant = 'horizontal',
  onItemClick,
}) => {
  const [activeSection, setActiveSection] = useState<string>('');

  useEffect(() => {
    const handleScroll = () => {
      const sections = items
        .filter(item => !item.external && item.href.startsWith('#'))
        .map(item => item.href.substring(1));

      let currentSection = '';
      
      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = `#${section}`;
            break;
          }
        }
      }
      
      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial position
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [items]);

  const handleNavClick = (item: NavItem) => {
    if (item.external) {
      window.open(item.href, '_blank', 'noopener,noreferrer');
    } else if (item.href.startsWith('#')) {
      const sectionId = item.href.substring(1);
      scrollToElement(sectionId, 80); // 80px offset for fixed header
    } else {
      // Handle internal routing if needed
      window.location.href = item.href;
    }
    
    if (onItemClick) {
      onItemClick(item);
    }
  };

  const baseClasses = variant === 'horizontal' 
    ? 'flex items-center space-x-8' 
    : 'flex flex-col space-y-4';

  return (
    <nav className={cn(baseClasses, className)}>
      {items.map((item, index) => {
        const isActive = activeSection === item.href;
        
        return (
          <button
            key={index}
            onClick={() => handleNavClick(item)}
            className={cn(
              'relative text-sm font-medium transition-colors duration-200 hover:text-primary-600 focus:outline-none focus:text-primary-600',
              isActive 
                ? 'text-primary-600' 
                : 'text-secondary-700 hover:text-primary-600',
              variant === 'vertical' && 'text-left w-full py-2'
            )}
          >
            {item.label}
            {item.external && (
              <svg
                className="inline-block ml-1 h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                />
              </svg>
            )}
            
            {/* Active indicator */}
            {isActive && variant === 'horizontal' && (
              <span className="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary-600 rounded-full" />
            )}
            
            {isActive && variant === 'vertical' && (
              <span className="absolute left-0 top-0 bottom-0 w-0.5 bg-primary-600 rounded-full" />
            )}
          </button>
        );
      })}
    </nav>
  );
};

export default Navigation;
