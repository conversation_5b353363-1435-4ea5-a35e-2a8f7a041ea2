import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Sphere, Box, Torus, Icosahedron, MeshDistortMaterial, Float, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { useDeviceOptimizations } from '../../hooks/useResponsive';

interface Narrative3DElementProps {
  type: 'vision' | 'development' | 'automation' | 'results';
  isActive: boolean;
  scrollProgress: number;
  className?: string;
}

// Individual 3D components for each narrative step
const VisionElement: React.FC<{ isActive: boolean; scrollProgress: number }> = ({ isActive, scrollProgress }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.3;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.2;
      meshRef.current.scale.setScalar(isActive ? 1 + Math.sin(state.clock.elapsedTime) * 0.1 : 0.8);
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <Icosahedron ref={meshRef} args={[1, 0]} position={[0, 0, 0]}>
        <MeshDistortMaterial
          color={isActive ? "#0ea5e9" : "#64748b"}
          attach="material"
          distort={0.4}
          speed={2}
          roughness={0.1}
          metalness={0.8}
          transparent
          opacity={isActive ? 0.9 : 0.6}
        />
      </Icosahedron>
    </Float>
  );
};

const DevelopmentElement: React.FC<{ isActive: boolean; scrollProgress: number }> = ({ isActive, scrollProgress }) => {
  const groupRef = useRef<THREE.Group>(null);
  const boxes = useMemo(() => Array.from({ length: 8 }, (_, i) => i), []);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.5;
      groupRef.current.children.forEach((child, i) => {
        child.position.y = Math.sin(state.clock.elapsedTime + i) * 0.2;
      });
    }
  });

  return (
    <group ref={groupRef}>
      {boxes.map((_, i) => (
        <Box
          key={i}
          args={[0.3, 0.3, 0.3]}
          position={[
            Math.cos((i / boxes.length) * Math.PI * 2) * 1.5,
            0,
            Math.sin((i / boxes.length) * Math.PI * 2) * 1.5,
          ]}
        >
          <meshStandardMaterial
            color={isActive ? "#10b981" : "#64748b"}
            transparent
            opacity={isActive ? 0.8 : 0.5}
          />
        </Box>
      ))}
    </group>
  );
};

const AutomationElement: React.FC<{ isActive: boolean; scrollProgress: number }> = ({ isActive, scrollProgress }) => {
  const torusRef = useRef<THREE.Mesh>(null);
  const particlesRef = useRef<THREE.InstancedMesh>(null);
  
  const particleCount = 20;
  const particles = useMemo(() => {
    const temp = [];
    for (let i = 0; i < particleCount; i++) {
      temp.push({
        position: [
          (Math.random() - 0.5) * 4,
          (Math.random() - 0.5) * 4,
          (Math.random() - 0.5) * 4,
        ],
        speed: Math.random() * 0.02 + 0.01,
      });
    }
    return temp;
  }, []);

  useFrame((state) => {
    if (torusRef.current) {
      torusRef.current.rotation.x = state.clock.elapsedTime * 0.4;
      torusRef.current.rotation.z = state.clock.elapsedTime * 0.2;
    }

    if (particlesRef.current) {
      particles.forEach((particle, i) => {
        const matrix = new THREE.Matrix4();
        const time = state.clock.elapsedTime;
        matrix.setPosition(
          particle.position[0] + Math.sin(time * particle.speed) * 2,
          particle.position[1] + Math.cos(time * particle.speed) * 2,
          particle.position[2] + Math.sin(time * particle.speed * 0.5) * 1
        );
        matrix.scale(new THREE.Vector3(0.1, 0.1, 0.1));
        particlesRef.current!.setMatrixAt(i, matrix);
      });
      particlesRef.current.instanceMatrix.needsUpdate = true;
    }
  });

  return (
    <>
      <Torus ref={torusRef} args={[1, 0.3, 16, 32]}>
        <meshStandardMaterial
          color={isActive ? "#f59e0b" : "#64748b"}
          transparent
          opacity={isActive ? 0.8 : 0.5}
          wireframe
        />
      </Torus>
      <instancedMesh ref={particlesRef} args={[undefined, undefined, particleCount]}>
        <sphereGeometry args={[0.05, 8, 8]} />
        <meshBasicMaterial color={isActive ? "#f59e0b" : "#64748b"} transparent opacity={0.7} />
      </instancedMesh>
    </>
  );
};

const ResultsElement: React.FC<{ isActive: boolean; scrollProgress: number }> = ({ isActive, scrollProgress }) => {
  const groupRef = useRef<THREE.Group>(null);
  const bars = useMemo(() => Array.from({ length: 5 }, (_, i) => ({ height: Math.random() * 2 + 0.5, delay: i * 0.2 })), []);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.2;
      groupRef.current.children.forEach((child, i) => {
        const targetScale = isActive ? bars[i].height : 0.2;
        child.scale.y = THREE.MathUtils.lerp(child.scale.y, targetScale, 0.05);
      });
    }
  });

  return (
    <group ref={groupRef}>
      {bars.map((bar, i) => (
        <Box
          key={i}
          args={[0.3, 1, 0.3]}
          position={[(i - 2) * 0.5, 0, 0]}
        >
          <meshStandardMaterial
            color={isActive ? "#8b5cf6" : "#64748b"}
            transparent
            opacity={isActive ? 0.8 : 0.5}
          />
        </Box>
      ))}
    </group>
  );
};

const Narrative3DElement: React.FC<Narrative3DElementProps> = ({
  type,
  isActive,
  scrollProgress,
  className = '',
}) => {
  const { threeDComplexity, shouldReduceMotion } = useDeviceOptimizations();

  const renderElement = () => {
    switch (type) {
      case 'vision':
        return <VisionElement isActive={isActive} scrollProgress={scrollProgress} />;
      case 'development':
        return <DevelopmentElement isActive={isActive} scrollProgress={scrollProgress} />;
      case 'automation':
        return <AutomationElement isActive={isActive} scrollProgress={scrollProgress} />;
      case 'results':
        return <ResultsElement isActive={isActive} scrollProgress={scrollProgress} />;
      default:
        return null;
    }
  };

  return (
    <div className={`w-full h-64 ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 4], fov: 60 }}
        style={{ background: 'transparent' }}
        dpr={threeDComplexity === 'low' ? [1, 1] : [1, 2]}
        performance={{ min: threeDComplexity === 'low' ? 0.3 : 0.5 }}
        frameloop={shouldReduceMotion ? 'demand' : 'always'}
      >
        {/* Lighting */}
        <ambientLight intensity={0.6} />
        <directionalLight position={[5, 5, 5]} intensity={1} />
        <pointLight position={[-5, -5, -5]} intensity={0.5} />

        {/* Environment for reflections */}
        <Environment preset="city" />

        {/* Render the specific 3D element */}
        {renderElement()}
      </Canvas>
    </div>
  );
};

export default Narrative3DElement;