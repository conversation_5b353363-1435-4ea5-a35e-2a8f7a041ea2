import React from 'react';
import { cn } from '../../utils';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'light' | 'dark';
  onClick?: () => void;
}

const Logo: React.FC<LogoProps> = ({
  className,
  size = 'md',
  variant = 'dark',
  onClick
}) => {
  const sizeClasses = {
    sm: 'h-8 w-auto',
    md: 'h-10 w-auto',
    lg: 'h-12 w-auto',
  };
  
  const textSizeClasses = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl',
  };
  
  const colorClasses = {
    light: 'text-white',
    dark: 'text-secondary-900',
  };
  
  return (
    <div
      className={cn('flex items-center', onClick && 'cursor-pointer', className)}
      onClick={onClick}
    >
      {/* Logo Icon/Symbol */}
      <div className={cn(
        'flex items-center justify-center rounded-lg bg-gradient-to-br from-primary-500 to-accent-500 mr-3',
        sizeClasses[size]
      )}>
        <span className={cn(
          'font-bold text-white',
          size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-lg'
        )}>
          AB
        </span>
      </div>
      
      {/* Logo Text */}
      <div className="flex flex-col">
        <span className={cn(
          'font-heading font-bold leading-none',
          textSizeClasses[size],
          colorClasses[variant]
        )}>
          AptiBot
        </span>
        <span className={cn(
          'text-xs font-medium opacity-75',
          colorClasses[variant]
        )}>
          Full-Stack Solutions
        </span>
      </div>
    </div>
  );
};

export default Logo;
