import React from 'react';
import { cn } from '../utils';
import { Header, Footer } from '../components/organisms';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  footerClassName?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  className,
  headerClassName,
  footerClassName,
  showHeader = true,
  showFooter = true,
}) => {
  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {/* Header */}
      {showHeader && (
        <Header className={headerClassName} />
      )}
      
      {/* Main Content */}
      <main className={cn(
        'flex-1',
        showHeader && 'pt-6' // Reduced padding for floating header
      )}>
        {children}
      </main>
      
      {/* Footer */}
      {showFooter && (
        <Footer className={footerClassName} />
      )}
    </div>
  );
};

export default MainLayout;
