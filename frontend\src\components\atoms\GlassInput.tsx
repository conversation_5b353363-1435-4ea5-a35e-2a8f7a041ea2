import React, { forwardRef } from 'react';
import { cn } from '../../utils';

interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const GlassInput = forwardRef<HTMLInputElement, GlassInputProps>(({
  label,
  error,
  icon,
  variant = 'default',
  size = 'md',
  className,
  ...props
}, ref) => {
  const baseStyles = {
    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
    backdropFilter: 'blur(20px) saturate(180%)',
    WebkitBackdropFilter: 'blur(20px) saturate(180%)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
  };

  const variantStyles = {
    default: {},
    primary: {
      border: '1px solid rgba(6, 182, 212, 0.3)',
    },
    secondary: {
      border: '1px solid rgba(147, 51, 234, 0.3)',
    },
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm rounded-lg',
    md: 'px-4 py-3 text-base rounded-xl',
    lg: 'px-6 py-4 text-lg rounded-xl',
  };

  const focusClasses = error
    ? 'focus:border-red-400/50 focus:ring-2 focus:ring-red-400/20'
    : variant === 'primary'
    ? 'focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20'
    : variant === 'secondary'
    ? 'focus:border-purple-400/50 focus:ring-2 focus:ring-purple-400/20'
    : 'focus:border-white/50 focus:ring-2 focus:ring-white/20';

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-white/90">
          {label}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60">
            {icon}
          </div>
        )}
        
        <input
          ref={ref}
          className={cn(
            'w-full text-white placeholder-white/50 transition-all duration-300 outline-none',
            sizeClasses[size],
            focusClasses,
            icon && 'pl-10',
            error && 'border-red-400/50',
            className
          )}
          style={{
            ...baseStyles,
            ...variantStyles[variant],
          }}
          {...props}
        />
        
        {/* Focus glow effect */}
        <div 
          className="absolute inset-0 rounded-inherit opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-100"
          style={{
            background: variant === 'primary' 
              ? 'linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)'
              : variant === 'secondary'
              ? 'linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)',
            filter: 'blur(1px)',
          }}
        />
      </div>
      
      {error && (
        <p className="text-red-400 text-sm mt-1 animate-fade-in">
          {error}
        </p>
      )}
    </div>
  );
});

GlassInput.displayName = 'GlassInput';

export default GlassInput;
