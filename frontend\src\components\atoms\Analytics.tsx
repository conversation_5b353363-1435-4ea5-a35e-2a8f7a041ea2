import React, { useEffect } from 'react';

interface AnalyticsProps {
  trackingId?: string;
  enableInDevelopment?: boolean;
}

// Extend Window interface for gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

const Analytics: React.FC<AnalyticsProps> = ({
  trackingId = 'G-XXXXXXXXXX', // Replace with actual GA4 tracking ID
  enableInDevelopment = false,
}) => {
  const isDevelopment = import.meta.env.DEV;
  const shouldTrack = !isDevelopment || enableInDevelopment;

  useEffect(() => {
    if (!shouldTrack || !trackingId) return;

    // Initialize dataLayer
    window.dataLayer = window.dataLayer || [];
    
    // Define gtag function
    window.gtag = function gtag(...args: any[]) {
      window.dataLayer.push(args);
    };

    // Configure gtag
    window.gtag('js', new Date());
    window.gtag('config', trackingId, {
      page_title: document.title,
      page_location: window.location.href,
    });

    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
    document.head.appendChild(script);

    // Track initial page view
    trackPageView(window.location.pathname);

    // Clean up function
    return () => {
      // Remove script if component unmounts
      const existingScript = document.querySelector(`script[src*="${trackingId}"]`);
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, [trackingId, shouldTrack]);

  return null;
};

// Analytics utility functions
export const trackPageView = (path: string, title?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-XXXXXXXXXX', {
      page_path: path,
      page_title: title || document.title,
    });
  }
};

export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

export const trackCustomEvent = (eventName: string, parameters: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

// Performance monitoring
export const trackPerformance = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    // Track Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          
          // Track page load time
          trackEvent('page_load_time', 'performance', 'load_time', navEntry.loadEventEnd - navEntry.loadEventStart);
          
          // Track DOM content loaded
          trackEvent('dom_content_loaded', 'performance', 'dcl_time', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart);
          
          // Track first contentful paint
          if (navEntry.loadEventEnd > 0) {
            trackEvent('first_contentful_paint', 'performance', 'fcp_time', navEntry.loadEventEnd - navEntry.fetchStart);
          }
        }
        
        if (entry.entryType === 'largest-contentful-paint') {
          // Track Largest Contentful Paint (LCP)
          trackEvent('largest_contentful_paint', 'performance', 'lcp_time', entry.startTime);
        }
        
        if (entry.entryType === 'first-input') {
          // Track First Input Delay (FID)
          const fidEntry = entry as PerformanceEventTiming;
          trackEvent('first_input_delay', 'performance', 'fid_time', fidEntry.processingStart - fidEntry.startTime);
        }
      }
    });

    // Observe navigation and paint timings
    observer.observe({ entryTypes: ['navigation', 'largest-contentful-paint', 'first-input'] });

    // Track Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
    });
    
    clsObserver.observe({ entryTypes: ['layout-shift'] });

    // Send CLS value after page is hidden or unloaded
    const sendCLS = () => {
      trackEvent('cumulative_layout_shift', 'performance', 'cls_score', clsValue);
    };

    window.addEventListener('beforeunload', sendCLS);
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        sendCLS();
      }
    });
  }
};

// User engagement tracking
export const trackUserEngagement = () => {
  if (typeof window !== 'undefined') {
    let startTime = Date.now();
    let isActive = true;

    // Track time on page
    const trackTimeOnPage = () => {
      if (isActive) {
        const timeSpent = Math.round((Date.now() - startTime) / 1000);
        trackEvent('time_on_page', 'engagement', 'seconds', timeSpent);
      }
    };

    // Track scroll depth
    let maxScrollDepth = 0;
    const trackScrollDepth = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);
      
      if (scrollPercent > maxScrollDepth) {
        maxScrollDepth = scrollPercent;
        
        // Track milestone scroll depths
        if (scrollPercent >= 25 && scrollPercent < 50) {
          trackEvent('scroll_depth', 'engagement', '25_percent');
        } else if (scrollPercent >= 50 && scrollPercent < 75) {
          trackEvent('scroll_depth', 'engagement', '50_percent');
        } else if (scrollPercent >= 75 && scrollPercent < 100) {
          trackEvent('scroll_depth', 'engagement', '75_percent');
        } else if (scrollPercent >= 100) {
          trackEvent('scroll_depth', 'engagement', '100_percent');
        }
      }
    };

    // Track user activity
    const handleActivity = () => {
      isActive = true;
      startTime = Date.now();
    };

    const handleInactivity = () => {
      isActive = false;
    };

    // Event listeners
    window.addEventListener('scroll', trackScrollDepth, { passive: true });
    window.addEventListener('beforeunload', trackTimeOnPage);
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        trackTimeOnPage();
        handleInactivity();
      } else {
        handleActivity();
      }
    });

    // Track clicks on important elements
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      
      // Track button clicks
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const buttonText = target.textContent?.trim() || 'Unknown Button';
        trackEvent('button_click', 'interaction', buttonText);
      }
      
      // Track link clicks
      if (target.tagName === 'A' || target.closest('a')) {
        const link = target.closest('a') as HTMLAnchorElement;
        const linkText = link.textContent?.trim() || 'Unknown Link';
        const linkUrl = link.href;
        
        trackEvent('link_click', 'interaction', linkText, undefined);
        trackCustomEvent('link_click_detailed', {
          link_text: linkText,
          link_url: linkUrl,
          link_domain: new URL(linkUrl).hostname,
        });
      }
    });
  }
};

export default Analytics;
