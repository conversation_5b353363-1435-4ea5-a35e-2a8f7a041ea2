import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useForm } from '../index';

interface TestFormData {
  name: string;
  email: string;
  age: number;
}

describe('useForm Hook', () => {
  const initialValues: TestFormData = {
    name: '',
    email: '',
    age: 0,
  };

  it('initializes with default values', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.isValid).toBe(true);
  });

  it('updates values correctly', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    act(() => {
      result.current.setValue('name', '<PERSON>');
    });
    
    expect(result.current.values.name).toBe('<PERSON>');
  });

  it('sets and clears errors', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    act(() => {
      result.current.setError('email', 'Invalid email');
    });
    
    expect(result.current.errors.email).toBe('Invalid email');
    expect(result.current.isValid).toBe(false);
    
    act(() => {
      result.current.setError('email', '');
    });
    
    expect(result.current.errors.email).toBe('');
    expect(result.current.isValid).toBe(true);
  });

  it('tracks touched fields', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    act(() => {
      result.current.setTouched('name');
    });
    
    expect(result.current.touched.name).toBe(true);
  });

  it('resets form to initial values', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    act(() => {
      result.current.setValue('name', 'John Doe');
      result.current.setError('email', 'Invalid email');
      result.current.setTouched('name');
    });
    
    expect(result.current.values.name).toBe('John Doe');
    expect(result.current.errors.email).toBe('Invalid email');
    expect(result.current.touched.name).toBe(true);
    
    act(() => {
      result.current.reset();
    });
    
    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
  });

  it('calculates isValid correctly', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    expect(result.current.isValid).toBe(true);
    
    act(() => {
      result.current.setError('name', 'Required');
    });
    
    expect(result.current.isValid).toBe(false);
    
    act(() => {
      result.current.setError('email', 'Invalid');
    });
    
    expect(result.current.isValid).toBe(false);
    
    act(() => {
      result.current.setError('name', '');
      result.current.setError('email', '');
    });
    
    expect(result.current.isValid).toBe(true);
  });

  it('handles multiple field updates', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    act(() => {
      result.current.setValue('name', 'John');
      result.current.setValue('email', '<EMAIL>');
      result.current.setValue('age', 25);
    });
    
    expect(result.current.values).toEqual({
      name: 'John',
      email: '<EMAIL>',
      age: 25,
    });
  });

  it('preserves other values when updating single field', () => {
    const { result } = renderHook(() => useForm(initialValues));
    
    act(() => {
      result.current.setValue('name', 'John');
      result.current.setValue('email', '<EMAIL>');
    });
    
    act(() => {
      result.current.setValue('name', 'Jane');
    });
    
    expect(result.current.values).toEqual({
      name: 'Jane',
      email: '<EMAIL>',
      age: 0,
    });
  });
});
