// Google Analytics 4 integration
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
}

interface PageViewEvent {
  page_title: string;
  page_location: string;
  page_path: string;
}

class Analytics {
  private isInitialized = false;
  private trackingId: string | null = null;

  constructor() {
    this.trackingId = import.meta.env.VITE_GA_TRACKING_ID;
    this.init();
  }

  private init() {
    if (!this.trackingId || typeof window === 'undefined') {
      console.warn('Google Analytics tracking ID not found or running on server');
      return;
    }

    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.trackingId}`;
    document.head.appendChild(script);

    // Initialize dataLayer
    window.dataLayer = window.dataLayer || [];
    window.gtag = function gtag() {
      window.dataLayer.push(arguments);
    };

    // Configure Google Analytics
    window.gtag('js', new Date());
    window.gtag('config', this.trackingId, {
      page_title: document.title,
      page_location: window.location.href,
      page_path: window.location.pathname,
    });

    this.isInitialized = true;
    console.log('Google Analytics initialized');
  }

  // Track page views
  pageView(event: Partial<PageViewEvent> = {}) {
    if (!this.isInitialized || !window.gtag) return;

    const pageData = {
      page_title: document.title,
      page_location: window.location.href,
      page_path: window.location.pathname,
      ...event,
    };

    window.gtag('config', this.trackingId!, pageData);
  }

  // Track custom events
  event(event: AnalyticsEvent) {
    if (!this.isInitialized || !window.gtag) return;

    window.gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
    });
  }

  // Track form submissions
  formSubmit(formName: string, success: boolean = true) {
    this.event({
      action: 'form_submit',
      category: 'engagement',
      label: `${formName}_${success ? 'success' : 'error'}`,
    });
  }

  // Track button clicks
  buttonClick(buttonName: string, location: string) {
    this.event({
      action: 'click',
      category: 'engagement',
      label: `${buttonName}_${location}`,
    });
  }

  // Track scroll depth
  scrollDepth(percentage: number) {
    this.event({
      action: 'scroll',
      category: 'engagement',
      label: `${percentage}%`,
      value: percentage,
    });
  }

  // Track file downloads
  fileDownload(fileName: string, fileType: string) {
    this.event({
      action: 'file_download',
      category: 'engagement',
      label: `${fileName}.${fileType}`,
    });
  }

  // Track external link clicks
  externalLink(url: string) {
    this.event({
      action: 'click',
      category: 'outbound',
      label: url,
    });
  }

  // Track video interactions
  videoInteraction(action: 'play' | 'pause' | 'complete', videoTitle: string) {
    this.event({
      action: `video_${action}`,
      category: 'engagement',
      label: videoTitle,
    });
  }

  // Track search queries
  search(query: string, results: number) {
    this.event({
      action: 'search',
      category: 'engagement',
      label: query,
      value: results,
    });
  }

  // Track user timing
  timing(name: string, value: number, category: string = 'performance') {
    if (!this.isInitialized || !window.gtag) return;

    window.gtag('event', 'timing_complete', {
      name,
      value,
      event_category: category,
    });
  }

  // Track exceptions
  exception(description: string, fatal: boolean = false) {
    if (!this.isInitialized || !window.gtag) return;

    window.gtag('event', 'exception', {
      description,
      fatal,
    });
  }
}

// Create singleton instance
export const analytics = new Analytics();

// React hook for analytics
export const useAnalytics = () => {
  const trackPageView = (event?: Partial<PageViewEvent>) => {
    analytics.pageView(event);
  };

  const trackEvent = (event: AnalyticsEvent) => {
    analytics.event(event);
  };

  const trackFormSubmit = (formName: string, success: boolean = true) => {
    analytics.formSubmit(formName, success);
  };

  const trackButtonClick = (buttonName: string, location: string) => {
    analytics.buttonClick(buttonName, location);
  };

  const trackScrollDepth = (percentage: number) => {
    analytics.scrollDepth(percentage);
  };

  const trackExternalLink = (url: string) => {
    analytics.externalLink(url);
  };

  return {
    trackPageView,
    trackEvent,
    trackFormSubmit,
    trackButtonClick,
    trackScrollDepth,
    trackExternalLink,
  };
};

// Performance monitoring
export const performanceMonitoring = {
  // Track Core Web Vitals
  trackWebVitals: () => {
    if (typeof window === 'undefined') return;

    // First Contentful Paint
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
          analytics.timing('first_contentful_paint', Math.round(entry.startTime));
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['paint'] });
    } catch (e) {
      // Browser doesn't support this API
    }

    // Largest Contentful Paint
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        analytics.timing('largest_contentful_paint', Math.round(lastEntry.startTime));
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      // Browser doesn't support this API
    }

    // First Input Delay
    try {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fid = (entry as any).processingStart - entry.startTime;
          analytics.timing('first_input_delay', Math.round(fid));
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e) {
      // Browser doesn't support this API
    }
  },

  // Track custom performance metrics
  trackCustomMetric: (name: string, value: number) => {
    analytics.timing(name, value, 'custom_performance');
  },
};

// Error tracking
export const errorTracking = {
  // Track JavaScript errors
  trackError: (error: Error, context?: string) => {
    analytics.exception(`${context ? `${context}: ` : ''}${error.message}`, false);
    
    // Also log to console in development
    if (import.meta.env.DEV) {
      console.error('Tracked error:', error, context);
    }
  },

  // Track API errors
  trackApiError: (endpoint: string, status: number, message: string) => {
    analytics.exception(`API Error: ${endpoint} (${status}) - ${message}`, false);
  },

  // Track form validation errors
  trackFormError: (formName: string, fieldName: string, errorMessage: string) => {
    analytics.event({
      action: 'form_error',
      category: 'form_validation',
      label: `${formName}_${fieldName}_${errorMessage}`,
    });
  },
};
