import React from 'react';
import { cn } from '../../utils';
import { Button } from '../atoms';

interface AboutSectionProps {
  className?: string;
}

const AboutSection: React.FC<AboutSectionProps> = ({ className }) => {
  const handleLearnMore = () => {
    const servicesSection = document.getElementById('services');
    if (servicesSection) {
      servicesSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const stats = [
    { number: '50+', label: 'Projects Delivered' },
    { number: '100%', label: 'Client Satisfaction' },
    { number: '24/7', label: 'Support Available' },
    { number: '5+', label: 'Years Experience' },
  ];

  const values = [
    {
      icon: (
        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      title: 'Innovation First',
      description: 'We leverage cutting-edge technologies and methodologies to deliver solutions that are ahead of the curve.',
    },
    {
      icon: (
        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: 'Performance Driven',
      description: 'Every solution we build is optimized for speed, scalability, and exceptional user experience.',
    },
    {
      icon: (
        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      title: 'Client Partnership',
      description: 'We work as an extension of your team, ensuring transparent communication and collaborative success.',
    },
  ];

  return (
    <section 
      id="about" 
      className={cn('section-padding bg-white', className)}
    >
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="heading-lg text-secondary-900 mb-6">
            About AptiBot
          </h2>
          <p className="text-body text-secondary-600 max-w-3xl mx-auto">
            We're a team of passionate full-stack developers and automation specialists 
            dedicated to transforming businesses through innovative technology solutions.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Column - Content */}
          <div>
            <h3 className="heading-md text-secondary-900 mb-6">
              Empowering Businesses Through Technology
            </h3>
            <div className="space-y-4 text-body text-secondary-600">
              <p>
                At AptiBot, we specialize in delivering comprehensive <strong>full-stack web development</strong> solutions 
                that span from elegant frontend interfaces to robust backend systems. Our expertise extends to 
                powerful <strong>make.com automation</strong> that streamlines business processes and enhances productivity.
              </p>
              <p>
                Our team combines deep technical knowledge with creative problem-solving to build applications 
                that not only meet today's requirements but scale for tomorrow's growth. We believe in creating 
                digital experiences that are both beautiful and functional.
              </p>
              <p>
                Whether you're a startup looking to build your first application or an enterprise seeking to 
                modernize your systems, we have the expertise and passion to bring your vision to life.
              </p>
            </div>
            
            <div className="mt-8">
              <Button
                variant="primary"
                size="lg"
                onClick={handleLearnMore}
              >
                Explore Our Services
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Button>
            </div>
          </div>

          {/* Right Column - Stats */}
          <div className="bg-gradient-to-br from-primary-50 to-accent-50 rounded-2xl p-8">
            <h4 className="text-xl font-semibold text-secondary-900 mb-8 text-center">
              Our Impact in Numbers
            </h4>
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-primary-600 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-sm text-secondary-600">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div>
          <h3 className="heading-md text-center text-secondary-900 mb-12">
            Our Core Values
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div 
                key={index}
                className="text-center p-6 rounded-xl hover:bg-secondary-50 transition-colors duration-300"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 text-primary-600 rounded-full mb-4">
                  {value.icon}
                </div>
                <h4 className="text-xl font-semibold text-secondary-900 mb-3">
                  {value.title}
                </h4>
                <p className="text-secondary-600">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
