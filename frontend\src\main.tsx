import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { registerServiceWorker, showUpdateNotification, preloadCriticalResources } from './utils/serviceWorker'

// Register service worker for performance and offline functionality
if (import.meta.env.PROD) {
  registerServiceWorker({
    onUpdate: (registration) => {
      showUpdateNotification(registration);
    },
    onSuccess: () => {
      console.log('App is ready for offline use');
    },
    onError: (error) => {
      console.error('Service worker registration failed:', error);
    },
  });

  // Preload critical resources
  preloadCriticalResources();
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
