import React from 'react';
import { cn } from '../../utils';

interface ThreeDLoaderProps {
  className?: string;
  message?: string;
}

const ThreeDLoader: React.FC<ThreeDLoaderProps> = ({
  className,
  message = 'Loading 3D Experience...'
}) => {
  return (
    <div className={cn(
      'w-full h-full flex items-center justify-center bg-transparent',
      className
    )}>
      <div className="text-center">
        {/* Animated AptiBot Logo */}
        <div className="relative mb-6">
          {/* Outer rotating ring */}
          <div className="w-16 h-16 mx-auto relative">
            <div className="absolute inset-0 border-4 border-transparent border-t-cyan-400 border-r-cyan-400 rounded-full animate-spin"></div>
            <div className="absolute inset-2 border-4 border-transparent border-b-teal-400 border-l-teal-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
            
            {/* Center logo */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-teal-400 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AB</span>
              </div>
            </div>
          </div>
          
          {/* Pulsing glow effect */}
          <div className="absolute inset-0 w-16 h-16 mx-auto bg-cyan-400/20 rounded-full animate-pulse blur-xl"></div>
        </div>
        
        {/* Loading text */}
        <p className="text-cyan-400 font-medium text-lg mb-2 animate-pulse">
          {message}
        </p>
        
        {/* Progress dots */}
        <div className="flex justify-center space-x-1">
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        
        {/* Subtle hint text */}
        <p className="text-gray-400 text-sm mt-4 opacity-75">
          Preparing immersive experience...
        </p>
      </div>
    </div>
  );
};

export default ThreeDLoader;