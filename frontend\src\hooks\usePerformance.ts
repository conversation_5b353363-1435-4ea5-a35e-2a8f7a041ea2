import { useEffect, useCallback } from 'react';
import { usePerformanceMonitor } from './usePerformanceMonitor';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

interface UsePerformanceOptions {
  enableLogging?: boolean;
  enableReporting?: boolean;
  reportingEndpoint?: string;
}

export const usePerformance = (options: UsePerformanceOptions = {}) => {
  const { enableLogging = false, enableReporting = false, reportingEndpoint } = options;

  const logMetric = useCallback((name: string, value: number) => {
    if (enableLogging) {
      console.log(`Performance Metric - ${name}: ${value}ms`);
    }
  }, [enableLogging]);

  const reportMetric = useCallback(async (metrics: PerformanceMetrics) => {
    if (enableReporting && reportingEndpoint) {
      try {
        await fetch(reportingEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: Date.now(),
            metrics,
          }),
        });
      } catch (error) {
        console.error('Failed to report performance metrics:', error);
      }
    }
  }, [enableReporting, reportingEndpoint]);

  const measureWebVitals = useCallback(() => {
    const metrics: PerformanceMetrics = {};

    // First Contentful Paint
    const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry;
    if (fcpEntry) {
      metrics.fcp = fcpEntry.startTime;
      logMetric('First Contentful Paint', fcpEntry.startTime);
    }

    // Time to First Byte
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      logMetric('Time to First Byte', metrics.ttfb);
    }

    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          if (lastEntry) {
            metrics.lcp = lastEntry.startTime;
            logMetric('Largest Contentful Paint', lastEntry.startTime);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            metrics.fid = entry.processingStart - entry.startTime;
            logMetric('First Input Delay', metrics.fid);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          metrics.cls = clsValue;
          logMetric('Cumulative Layout Shift', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });

        // Report metrics after page load
        setTimeout(() => {
          reportMetric(metrics);
        }, 5000);

      } catch (error) {
        console.error('Performance observation failed:', error);
      }
    }

    return metrics;
  }, [logMetric, reportMetric]);

  const measureCustomMetric = useCallback((name: string, startTime?: number) => {
    const endTime = performance.now();
    const duration = startTime ? endTime - startTime : endTime;
    
    logMetric(name, duration);
    
    // Mark the measurement
    performance.mark(`${name}-end`);
    
    return duration;
  }, [logMetric]);

  const startCustomMetric = useCallback((name: string) => {
    const startTime = performance.now();
    performance.mark(`${name}-start`);
    
    return {
      end: () => measureCustomMetric(name, startTime),
      startTime,
    };
  }, [measureCustomMetric]);

  const getResourceMetrics = useCallback(() => {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    const metrics = {
      totalResources: resources.length,
      totalSize: 0,
      totalDuration: 0,
      slowestResource: { name: '', duration: 0 },
      resourceTypes: {} as Record<string, number>,
    };

    resources.forEach((resource) => {
      const duration = resource.responseEnd - resource.startTime;
      metrics.totalDuration += duration;
      
      if (resource.transferSize) {
        metrics.totalSize += resource.transferSize;
      }
      
      if (duration > metrics.slowestResource.duration) {
        metrics.slowestResource = { name: resource.name, duration };
      }
      
      // Categorize by resource type
      const type = resource.name.split('.').pop()?.toLowerCase() || 'other';
      metrics.resourceTypes[type] = (metrics.resourceTypes[type] || 0) + 1;
    });

    if (enableLogging) {
      console.table(metrics);
    }

    return metrics;
  }, [enableLogging]);

  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const metrics = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
      };

      if (enableLogging) {
        console.log('Memory Usage:', metrics);
      }

      return metrics;
    }
    return null;
  }, [enableLogging]);

  useEffect(() => {
    // Start measuring when component mounts
    if (document.readyState === 'complete') {
      measureWebVitals();
    } else {
      window.addEventListener('load', measureWebVitals);
      return () => window.removeEventListener('load', measureWebVitals);
    }
  }, [measureWebVitals]);

  return {
    measureWebVitals,
    measureCustomMetric,
    startCustomMetric,
    getResourceMetrics,
    getMemoryUsage,
  };
};
