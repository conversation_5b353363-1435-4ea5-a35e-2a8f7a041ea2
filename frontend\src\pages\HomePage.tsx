import React, { Suspense, lazy } from 'react';
import { useState, useEffect } from 'react';
import { usePerformance } from '../hooks/usePerformance';
import { SEOH<PERSON>, GlassCard, GlassButton, GlassSection } from '../components/atoms';

// Lazy load sections for the immersive landing page
const Hero3DSection = lazy(() => import('../components/organisms/Hero3DSection'));
const ScrollNarrativeSection = lazy(() => import('../components/organisms/ScrollNarrativeSection'));

// Loading component for lazy sections
const SectionLoader = () => (
  <div className="section-padding flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
  </div>
);

const HomePage: React.FC = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);

  // Mouse parallax effect for floating elements
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll parallax effect for floating elements
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Initialize performance monitoring for landing page
  usePerformance({
    enableLogging: import.meta.env.DEV,
    enableReporting: import.meta.env.PROD,
    reportingEndpoint: '/api/analytics/performance',
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Enhanced Floating Elements with Glassmorphism - Full Page Coverage */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-30">
        {/* Floating orbs with glassmorphism */}
        <div
          className="absolute top-1/4 left-1/8 w-16 h-16 rounded-full opacity-30 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 20}px, ${mousePosition.y * 20}px) rotate(${scrollY * 0.1}deg)`,
            background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(6, 182, 212, 0.3)',
          }}
        />
        <div
          className="absolute top-3/4 right-1/8 w-12 h-12 rounded-full opacity-25 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * -15}px, ${mousePosition.y * -15}px) rotate(${scrollY * -0.15}deg)`,
            animationDelay: '1s',
            background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.3) 0%, rgba(236, 72, 153, 0.3) 100%)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)',
            boxShadow: '0 8px 32px rgba(147, 51, 234, 0.3)',
          }}
        />
        <div
          className="absolute top-1/2 left-1/6 w-8 h-8 rounded-full opacity-40 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 25}px, ${mousePosition.y * 25}px) rotate(${scrollY * 0.2}deg)`,
            animationDelay: '2s',
            background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.3) 0%, rgba(6, 182, 212, 0.3) 100%)',
            backdropFilter: 'blur(6px)',
            WebkitBackdropFilter: 'blur(6px)',
            boxShadow: '0 8px 32px rgba(16, 185, 129, 0.3)',
          }}
        />

        {/* Geometric shapes */}
        <div
          className="absolute top-1/3 right-1/4 w-6 h-6 opacity-20 animate-spin"
          style={{
            transform: `translate(${mousePosition.x * -10}px, ${mousePosition.y * -10}px)`,
            background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
            clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
            animationDuration: '8s',
          }}
        />
        <div
          className="absolute bottom-1/3 left-1/3 w-4 h-4 opacity-25 animate-bounce"
          style={{
            transform: `translate(${mousePosition.x * 15}px, ${mousePosition.y * 15}px)`,
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
            borderRadius: '2px',
            animationDelay: '0.5s',
            animationDuration: '3s',
          }}
        />
        
        {/* Additional floating elements for more coverage */}
        <div
          className="absolute top-1/5 right-1/3 w-10 h-10 rounded-full opacity-20 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 12}px, ${mousePosition.y * 12}px) rotate(${scrollY * 0.08}deg)`,
            animationDelay: '3s',
            background: 'linear-gradient(135deg, rgba(251, 191, 36, 0.3) 0%, rgba(245, 158, 11, 0.3) 100%)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)',
            boxShadow: '0 8px 32px rgba(251, 191, 36, 0.3)',
          }}
        />
        <div
          className="absolute bottom-1/5 right-1/5 w-5 h-5 opacity-30 animate-spin"
          style={{
            transform: `translate(${mousePosition.x * -8}px, ${mousePosition.y * -8}px)`,
            background: 'linear-gradient(45deg, rgba(168, 85, 247, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%)',
            clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
            animationDuration: '12s',
          }}
        />
      </div>

      {/* SEO Optimization */}
      <SEOHead
        title="AptiBot - Full-Stack Development & Business Automation Solutions"
        description="Transform your business with cutting-edge full-stack web development and intelligent automation solutions. Expert React, TypeScript, and cloud development services."
        keywords="full-stack development, web development, React, TypeScript, automation, business solutions, cloud development, API development, modern web applications"
        url="https://aptibot.com"
        type="website"
      />

      {/* Immersive Landing Page - Enhanced 3D Hero Section */}
      <Suspense fallback={<SectionLoader />}>
        <Hero3DSection />
      </Suspense>

      {/* Scroll-Telling Narrative Sections */}
      <Suspense fallback={<SectionLoader />}>
        <ScrollNarrativeSection />
      </Suspense>
      
      {/* Services Section with Glassmorphism */}
      <GlassSection variant="dark" padding="lg">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Discover Our
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400"> Full-Stack Solutions</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            From concept to deployment, we build comprehensive digital solutions
            that transform your business and drive growth.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            {[
              {
                icon: (
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25" />
                  </svg>
                ),
                title: "Web Development",
                description: "Modern, responsive websites and applications built with cutting-edge technologies.",
                variant: "primary"
              },
              {
                icon: (
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                title: "Automation",
                description: "Streamline your business processes with intelligent automation solutions.",
                variant: "secondary"
              },
              {
                icon: (
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                ),
                title: "Consulting",
                description: "Expert technical guidance to help you make the right technology decisions.",
                variant: "accent"
              }
            ].map((service, index) => (
              <GlassCard
                key={index}
                variant={service.variant as "primary" | "secondary" | "accent"}
                size="lg"
                hover
                glow
                onClick={() => window.location.href = '/services'}
                className="text-center cursor-pointer"
              >
                <div className="text-cyan-400 mb-6 transform group-hover:scale-110 transition-transform duration-500">
                  {service.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                  {service.title}
                </h3>
                <p className="text-gray-300 group-hover:text-gray-200 transition-colors duration-300">
                  {service.description}
                </p>
              </GlassCard>
            ))}
          </div>
        </div>
      </GlassSection>

      {/* Call to Action Section with Glassmorphism */}
      <GlassSection variant="dark" padding="xl" particles>
        <div className="container mx-auto px-4 text-center">
          <GlassCard variant="primary" size="xl" hover glow className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
              Ready to <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Transform Your Business?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8">
              Let's discuss how our full-stack solutions can help you achieve your goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GlassButton
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/contact'}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                Start Your Project
              </GlassButton>
              <GlassButton
                variant="outline"
                size="lg"
                onClick={() => window.location.href = '/services'}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                Explore Services
              </GlassButton>
            </div>
          </GlassCard>
        </div>
      </GlassSection>
    </div>
  );
};

export default HomePage;
