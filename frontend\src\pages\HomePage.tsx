import React, { Suspense, lazy } from 'react';
import { usePerformance } from '../hooks/usePerformance';
import { SEOHead } from '../components/atoms';

// Lazy load sections for the immersive landing page
const Hero3DSection = lazy(() => import('../components/organisms/Hero3DSection'));
const ScrollNarrativeSection = lazy(() => import('../components/organisms/ScrollNarrativeSection'));

// Loading component for lazy sections
const SectionLoader = () => (
  <div className="section-padding flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
  </div>
);

const HomePage: React.FC = () => {
  // Initialize performance monitoring for landing page
  usePerformance({
    enableLogging: import.meta.env.DEV,
    enableReporting: import.meta.env.PROD,
    reportingEndpoint: '/api/analytics/performance',
  });

  return (
    <div className="min-h-screen">
      {/* SEO Optimization */}
      <SEOHead
        title="AptiBot - Full-Stack Development & Business Automation Solutions"
        description="Transform your business with cutting-edge full-stack web development and intelligent automation solutions. Expert React, TypeScript, and cloud development services."
        keywords="full-stack development, web development, React, TypeScript, automation, business solutions, cloud development, API development, modern web applications"
        url="https://aptibot.com"
        type="website"
      />

      {/* Immersive Landing Page - Enhanced 3D Hero Section */}
      <Suspense fallback={<SectionLoader />}>
        <Hero3DSection />
      </Suspense>

      {/* Scroll-Telling Narrative Sections */}
      <Suspense fallback={<SectionLoader />}>
        <ScrollNarrativeSection />
      </Suspense>
      
      {/* Placeholder for scroll narrative sections */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
            Discover Our
            <span className="text-primary-600"> Full-Stack Solutions</span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed mb-8">
            From concept to deployment, we build comprehensive digital solutions 
            that transform your business and drive growth.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            {[
              {
                icon: "💻",
                title: "Web Development",
                description: "Modern, responsive websites and applications built with cutting-edge technologies.",
                gradient: "from-blue-500/10 to-cyan-500/10",
                hoverGradient: "from-blue-500/20 to-cyan-500/20",
                borderGlow: "hover:border-blue-400/50"
              },
              {
                icon: "🤖",
                title: "Automation",
                description: "Streamline your business processes with intelligent automation solutions.",
                gradient: "from-purple-500/10 to-pink-500/10",
                hoverGradient: "from-purple-500/20 to-pink-500/20",
                borderGlow: "hover:border-purple-400/50"
              },
              {
                icon: "🎯",
                title: "Consulting",
                description: "Expert technical guidance to help you make the right technology decisions.",
                gradient: "from-emerald-500/10 to-teal-500/10",
                hoverGradient: "from-emerald-500/20 to-teal-500/20",
                borderGlow: "hover:border-emerald-400/50"
              }
            ].map((service, index) => (
              <div
                key={index}
                className={`group relative p-8 rounded-2xl transition-all duration-700 hover:scale-105 hover:-translate-y-3 cursor-pointer bg-gradient-to-br ${service.gradient} backdrop-blur-sm border border-white/20 ${service.borderGlow} shadow-xl hover:shadow-2xl`}
                style={{
                  background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)`,
                  backdropFilter: 'blur(20px) saturate(180%)',
                  WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                }}
              >
                {/* Card glow effect */}
                <div
                  className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-700 bg-gradient-to-br ${service.hoverGradient}`}
                  style={{ filter: 'blur(1px)' }}
                />

                {/* Card content */}
                <div className="relative z-10">
                  <div className="text-4xl mb-6 transform group-hover:scale-110 transition-transform duration-500">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-4 group-hover:text-primary-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-secondary-600 group-hover:text-secondary-700 transition-colors duration-300">
                    {service.description}
                  </p>
                </div>

                {/* Subtle inner glow */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="section-padding bg-primary-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-primary-100 max-w-2xl mx-auto leading-relaxed mb-8">
            Let's discuss how our full-stack solutions can help you achieve your goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/contact" 
              className="btn-secondary bg-white text-primary-600 hover:bg-primary-50"
            >
              Start Your Project
            </a>
            <a 
              href="/services" 
              className="btn-outline border-white text-white hover:bg-white hover:text-primary-600"
            >
              Explore Services
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
