import React, { Suspense, lazy } from 'react';
import { usePerformance } from '../hooks/usePerformance';

// Lazy load sections for the immersive landing page
const Hero3DSection = lazy(() => import('../components/organisms/Hero3DSection'));
const ScrollNarrativeSection = lazy(() => import('../components/organisms/ScrollNarrativeSection'));

// Loading component for lazy sections
const SectionLoader = () => (
  <div className="section-padding flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
  </div>
);

const HomePage: React.FC = () => {
  // Initialize performance monitoring for landing page
  usePerformance({
    enableLogging: import.meta.env.DEV,
    enableReporting: import.meta.env.PROD,
    reportingEndpoint: '/api/analytics/performance',
  });

  return (
    <div className="min-h-screen">
      {/* Immersive Landing Page - Enhanced 3D Hero Section */}
      <Suspense fallback={<SectionLoader />}>
        <Hero3DSection />
      </Suspense>

      {/* Scroll-Telling Narrative Sections */}
      <Suspense fallback={<SectionLoader />}>
        <ScrollNarrativeSection />
      </Suspense>
      
      {/* Placeholder for scroll narrative sections */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
            Discover Our
            <span className="text-primary-600"> Full-Stack Solutions</span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed mb-8">
            From concept to deployment, we build comprehensive digital solutions 
            that transform your business and drive growth.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">Web Development</h3>
              <p className="text-secondary-600">Modern, responsive websites and applications built with cutting-edge technologies.</p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">Automation</h3>
              <p className="text-secondary-600">Streamline your business processes with intelligent automation solutions.</p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">Consulting</h3>
              <p className="text-secondary-600">Expert technical guidance to help you make the right technology decisions.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="section-padding bg-primary-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-primary-100 max-w-2xl mx-auto leading-relaxed mb-8">
            Let's discuss how our full-stack solutions can help you achieve your goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/contact" 
              className="btn-secondary bg-white text-primary-600 hover:bg-primary-50"
            >
              Start Your Project
            </a>
            <a 
              href="/services" 
              className="btn-outline border-white text-white hover:bg-white hover:text-primary-600"
            >
              Explore Services
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
