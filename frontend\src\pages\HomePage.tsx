import React, { Suspense, lazy } from 'react';
import { usePerformance } from '../hooks/usePerformance';
import { <PERSON><PERSON><PERSON>, GlassCard, GlassButton, GlassSection } from '../components/atoms';

// Lazy load sections for the immersive landing page
const Hero3DSection = lazy(() => import('../components/organisms/Hero3DSection'));
const ScrollNarrativeSection = lazy(() => import('../components/organisms/ScrollNarrativeSection'));

// Loading component for lazy sections
const SectionLoader = () => (
  <div className="section-padding flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
  </div>
);

const HomePage: React.FC = () => {
  // Initialize performance monitoring for landing page
  usePerformance({
    enableLogging: import.meta.env.DEV,
    enableReporting: import.meta.env.PROD,
    reportingEndpoint: '/api/analytics/performance',
  });

  return (
    <div className="min-h-screen">
      {/* SEO Optimization */}
      <SEOHead
        title="AptiBot - Full-Stack Development & Business Automation Solutions"
        description="Transform your business with cutting-edge full-stack web development and intelligent automation solutions. Expert React, TypeScript, and cloud development services."
        keywords="full-stack development, web development, React, TypeScript, automation, business solutions, cloud development, API development, modern web applications"
        url="https://aptibot.com"
        type="website"
      />

      {/* Immersive Landing Page - Enhanced 3D Hero Section */}
      <Suspense fallback={<SectionLoader />}>
        <Hero3DSection />
      </Suspense>

      {/* Scroll-Telling Narrative Sections */}
      <Suspense fallback={<SectionLoader />}>
        <ScrollNarrativeSection />
      </Suspense>
      
      {/* Services Section with Glassmorphism */}
      <GlassSection variant="light" padding="lg">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Discover Our
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400"> Full-Stack Solutions</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            From concept to deployment, we build comprehensive digital solutions
            that transform your business and drive growth.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            {[
              {
                icon: "💻",
                title: "Web Development",
                description: "Modern, responsive websites and applications built with cutting-edge technologies.",
                variant: "primary"
              },
              {
                icon: "🤖",
                title: "Automation",
                description: "Streamline your business processes with intelligent automation solutions.",
                variant: "secondary"
              },
              {
                icon: "🎯",
                title: "Consulting",
                description: "Expert technical guidance to help you make the right technology decisions.",
                variant: "accent"
              }
            ].map((service, index) => (
              <GlassCard
                key={index}
                variant={service.variant as "primary" | "secondary" | "accent"}
                size="lg"
                hover
                glow
                onClick={() => window.location.href = '/services'}
                className="text-center cursor-pointer"
              >
                <div className="text-4xl mb-6 transform group-hover:scale-110 transition-transform duration-500">
                  {service.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                  {service.title}
                </h3>
                <p className="text-gray-300 group-hover:text-gray-200 transition-colors duration-300">
                  {service.description}
                </p>
              </GlassCard>
            ))}
          </div>
        </div>
      </GlassSection>

      {/* Call to Action Section with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4 text-center">
          <GlassCard variant="primary" size="xl" hover glow className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
              Ready to <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Transform Your Business?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8">
              Let's discuss how our full-stack solutions can help you achieve your goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GlassButton
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/contact'}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                Start Your Project
              </GlassButton>
              <GlassButton
                variant="outline"
                size="lg"
                onClick={() => window.location.href = '/services'}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                Explore Services
              </GlassButton>
            </div>
          </GlassCard>
        </div>
      </GlassSection>
    </div>
  );
};

export default HomePage;
