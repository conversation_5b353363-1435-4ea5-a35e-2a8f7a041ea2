/* Enhanced Glassmorphism Effects */

/* Floating animation keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(120deg); }
  66% { transform: translateY(5px) rotate(240deg); }
}

@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3), 0 0 40px rgba(6, 182, 212, 0.1);
    opacity: 0.8;
  }
  50% { 
    box-shadow: 0 0 30px rgba(6, 182, 212, 0.5), 0 0 60px rgba(6, 182, 212, 0.2);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes card-entrance {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Glassmorphism card base styles */
.glass-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(6, 182, 212, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced button with glassmorphism */
.glass-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.8) 0%, rgba(59, 130, 246, 0.8) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 30px rgba(6, 182, 212, 0.4),
    0 0 20px rgba(6, 182, 212, 0.3);
}

/* Floating orb animations */
.floating-orb {
  animation: float 6s ease-in-out infinite;
}

.floating-orb:nth-child(2) {
  animation-delay: -2s;
  animation-duration: 8s;
}

.floating-orb:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 7s;
}

/* Glow effect for cards */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.7s ease;
  pointer-events: none;
}

.glow-effect:hover::after {
  opacity: 1;
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
}

/* Responsive glassmorphism adjustments */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
  }
  
  .glass-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Performance optimizations */
.glass-card,
.glass-button,
.floating-orb {
  will-change: transform;
  transform-style: preserve-3d;
}

/* Entrance animations */
.animate-entrance {
  animation: card-entrance 0.8s ease-out forwards;
}

.animate-entrance:nth-child(1) { animation-delay: 0.1s; }
.animate-entrance:nth-child(2) { animation-delay: 0.2s; }
.animate-entrance:nth-child(3) { animation-delay: 0.3s; }

/* Shimmer effect for loading states */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}
