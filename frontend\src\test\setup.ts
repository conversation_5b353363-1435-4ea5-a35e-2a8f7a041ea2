import '@testing-library/jest-dom';

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_API_BASE_URL: 'http://localhost:5173',
    VITE_CONTACT_API_ENDPOINT: '/api/contact',
    VITE_SPLINE_SCENE_URL: 'https://prod.spline.design/test/scene.splinecode',
    VITE_NODE_ENV: 'test',
  },
  writable: true,
});

// Mock fetch for API calls
global.fetch = vi.fn();

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock scrollTo
global.scrollTo = vi.fn();

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock Spline
vi.mock('@splinetool/react-spline', () => ({
  default: vi.fn(() => <div data-testid="spline-mock">Spline Component</div>),
}));

// Clean up after each test
afterEach(() => {
  vi.clearAllMocks();
});
