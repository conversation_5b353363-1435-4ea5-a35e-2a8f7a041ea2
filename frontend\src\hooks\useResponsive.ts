import { useState, useEffect } from 'react';

interface BreakpointConfig {
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
}

const defaultBreakpoints: BreakpointConfig = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

interface ResponsiveState {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  breakpoint: keyof BreakpointConfig | 'xs';
  orientation: 'portrait' | 'landscape';
  isTouch: boolean;
  pixelRatio: number;
}

export const useResponsive = (breakpoints: BreakpointConfig = defaultBreakpoints) => {
  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isLargeDesktop: false,
        breakpoint: 'lg' as const,
        orientation: 'landscape' as const,
        isTouch: false,
        pixelRatio: 1,
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      width,
      height,
      isMobile: width < breakpoints.md,
      isTablet: width >= breakpoints.md && width < breakpoints.lg,
      isDesktop: width >= breakpoints.lg && width < breakpoints['2xl'],
      isLargeDesktop: width >= breakpoints['2xl'],
      breakpoint: getBreakpoint(width, breakpoints),
      orientation: height > width ? 'portrait' : 'landscape',
      isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      pixelRatio: window.devicePixelRatio || 1,
    };
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setState({
        width,
        height,
        isMobile: width < breakpoints.md,
        isTablet: width >= breakpoints.md && width < breakpoints.lg,
        isDesktop: width >= breakpoints.lg && width < breakpoints['2xl'],
        isLargeDesktop: width >= breakpoints['2xl'],
        breakpoint: getBreakpoint(width, breakpoints),
        orientation: height > width ? 'portrait' : 'landscape',
        isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
        pixelRatio: window.devicePixelRatio || 1,
      });
    };

    // Debounce resize events for better performance
    let timeoutId: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', debouncedResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      window.removeEventListener('orientationchange', handleResize);
      clearTimeout(timeoutId);
    };
  }, [breakpoints]);

  return state;
};

function getBreakpoint(width: number, breakpoints: BreakpointConfig): keyof BreakpointConfig | 'xs' {
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
}

// Hook for specific breakpoint checks
export const useBreakpoint = (breakpoint: keyof BreakpointConfig) => {
  const { breakpoint: currentBreakpoint } = useResponsive();
  
  const breakpointOrder: (keyof BreakpointConfig | 'xs')[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  const targetIndex = breakpointOrder.indexOf(breakpoint);
  
  return {
    isExact: currentBreakpoint === breakpoint,
    isAbove: currentIndex > targetIndex,
    isBelow: currentIndex < targetIndex,
    isAtLeast: currentIndex >= targetIndex,
    isAtMost: currentIndex <= targetIndex,
  };
};

// Hook for media query-like functionality
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    const handleChange = (e: MediaQueryListEvent) => setMatches(e.matches);

    mediaQuery.addEventListener('change', handleChange);
    setMatches(mediaQuery.matches);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);

  return matches;
};

// Hook for device-specific optimizations
export const useDeviceOptimizations = () => {
  const { isMobile, isTouch, pixelRatio } = useResponsive();
  
  return {
    // Reduce animations on mobile for better performance
    shouldReduceMotion: isMobile || window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    
    // Use lower quality images on mobile
    imageQuality: isMobile ? 60 : 80,
    
    // Adjust 3D complexity based on device
    threeDComplexity: isMobile ? 'low' : pixelRatio > 2 ? 'high' : 'medium',
    
    // Touch-specific optimizations
    touchOptimizations: isTouch,
    
    // Lazy loading threshold
    lazyLoadingThreshold: isMobile ? '100px' : '200px',
  };
};

// Hook for responsive values
export const useResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}) => {
  const { breakpoint } = useResponsive();
  
  // Find the appropriate value based on current breakpoint
  const breakpointOrder: (keyof BreakpointConfig | 'xs')[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  const currentIndex = breakpointOrder.indexOf(breakpoint);
  
  // Look for the value at current breakpoint or the closest smaller one
  for (let i = currentIndex; i >= 0; i--) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  // Fallback to the smallest available value
  for (const bp of breakpointOrder) {
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  return undefined;
};
