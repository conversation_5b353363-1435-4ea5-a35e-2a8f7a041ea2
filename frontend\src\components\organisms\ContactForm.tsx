import React, { useState } from 'react';
import { useForm } from '../../hooks';
import { Button, Input, Textarea, Select } from '../atoms';
import { ContactFormData, ContactFormProps, SelectOption } from '../../types';
import { cn } from '../../utils';

const ContactForm: React.FC<ContactFormProps> = ({ onSubmit, className }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState('');

  const initialValues: ContactFormData = {
    name: '',
    email: '',
    company: '',
    serviceType: '',
    projectDetails: '',
    budgetRange: '',
    timeline: '',
  };

  const {
    values,
    errors,
    touched,
    setValue,
    setError,
    setTouched: setFieldTouched,
    reset,
    isValid,
  } = useForm<ContactFormData>(initialValues);

  // Form options
  const serviceOptions: SelectOption[] = [
    { value: 'web-development', label: 'Web Development' },
    { value: 'mobile-app', label: 'Mobile App Development' },
    { value: 'automation', label: 'Business Process Automation' },
    { value: 'ai-integration', label: 'AI Integration' },
    { value: 'consulting', label: 'Technical Consulting' },
    { value: 'maintenance', label: 'Maintenance & Support' },
    { value: 'other', label: 'Other' },
  ];

  const budgetOptions: SelectOption[] = [
    { value: 'under-5k', label: 'Under $5,000' },
    { value: '5k-15k', label: '$5,000 - $15,000' },
    { value: '15k-50k', label: '$15,000 - $50,000' },
    { value: '50k-100k', label: '$50,000 - $100,000' },
    { value: 'over-100k', label: 'Over $100,000' },
    { value: 'discuss', label: 'Let\'s discuss' },
  ];

  const timelineOptions: SelectOption[] = [
    { value: 'asap', label: 'ASAP' },
    { value: '1-month', label: 'Within 1 month' },
    { value: '2-3-months', label: '2-3 months' },
    { value: '3-6-months', label: '3-6 months' },
    { value: '6-months-plus', label: '6+ months' },
    { value: 'flexible', label: 'Flexible timeline' },
  ];

  // Validation rules
  const validateField = (name: keyof ContactFormData, value: string) => {
    switch (name) {
      case 'name':
        if (!value.trim()) return 'Name is required';
        if (value.trim().length < 2) return 'Name must be at least 2 characters';
        break;
      case 'email':
        if (!value.trim()) return 'Email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return 'Please enter a valid email address';
        break;
      case 'serviceType':
        if (!value) return 'Please select a service type';
        break;
      case 'projectDetails':
        if (!value.trim()) return 'Project details are required';
        if (value.trim().length < 10) return 'Please provide more details (at least 10 characters)';
        break;
      case 'budgetRange':
        if (!value) return 'Please select a budget range';
        break;
      case 'timeline':
        if (!value) return 'Please select a timeline';
        break;
    }
    return '';
  };

  const handleFieldChange = (name: keyof ContactFormData, value: string) => {
    setValue(name, value);
    setFieldTouched(name);
    
    // Validate field
    const error = validateField(name, value);
    if (error) {
      setError(name, error);
    }
  };

  const validateForm = (): boolean => {
    let hasErrors = false;
    
    Object.keys(values).forEach((key) => {
      const fieldName = key as keyof ContactFormData;
      const error = validateField(fieldName, values[fieldName]);
      if (error) {
        setError(fieldName, error);
        hasErrors = true;
      }
    });
    
    return !hasErrors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setSubmitStatus('error');
      setSubmitMessage('Please fix the errors above and try again.');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setSubmitMessage('');

    try {
      if (onSubmit) {
        await onSubmit(values);
      } else {
        // Simulate API call for now
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      setSubmitStatus('success');
      setSubmitMessage('Thank you! Your message has been sent successfully. We\'ll get back to you within 24 hours.');
      reset();
    } catch (error) {
      setSubmitStatus('error');
      setSubmitMessage('Sorry, there was an error sending your message. Please try again or contact us directly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('max-w-2xl mx-auto', className)}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Full Name"
            placeholder="John Doe"
            value={values.name}
            onChange={(value) => handleFieldChange('name', value)}
            error={touched.name ? errors.name : undefined}
            required
          />
          
          <Input
            label="Email Address"
            type="email"
            placeholder="<EMAIL>"
            value={values.email}
            onChange={(value) => handleFieldChange('email', value)}
            error={touched.email ? errors.email : undefined}
            required
          />
        </div>

        <Input
          label="Company Name"
          placeholder="Your Company (Optional)"
          value={values.company}
          onChange={(value) => handleFieldChange('company', value)}
          error={touched.company ? errors.company : undefined}
        />

        {/* Project Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Service Type"
            placeholder="Select a service"
            options={serviceOptions}
            value={values.serviceType}
            onChange={(value) => handleFieldChange('serviceType', value)}
            error={touched.serviceType ? errors.serviceType : undefined}
            required
          />
          
          <Select
            label="Budget Range"
            placeholder="Select budget range"
            options={budgetOptions}
            value={values.budgetRange}
            onChange={(value) => handleFieldChange('budgetRange', value)}
            error={touched.budgetRange ? errors.budgetRange : undefined}
            required
          />
        </div>

        <Select
          label="Project Timeline"
          placeholder="When do you need this completed?"
          options={timelineOptions}
          value={values.timeline}
          onChange={(value) => handleFieldChange('timeline', value)}
          error={touched.timeline ? errors.timeline : undefined}
          required
        />

        <Textarea
          label="Project Details"
          placeholder="Tell us about your project, goals, and any specific requirements..."
          rows={6}
          value={values.projectDetails}
          onChange={(value) => handleFieldChange('projectDetails', value)}
          error={touched.projectDetails ? errors.projectDetails : undefined}
          required
        />

        {/* Submit Status Messages */}
        {submitStatus === 'success' && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <p className="text-green-700">{submitMessage}</p>
            </div>
          </div>
        )}

        {submitStatus === 'error' && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-red-700">{submitMessage}</p>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isSubmitting}
            disabled={isSubmitting}
            className="w-full md:w-auto"
          >
            {isSubmitting ? 'Sending Message...' : 'Send Message'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ContactForm;
