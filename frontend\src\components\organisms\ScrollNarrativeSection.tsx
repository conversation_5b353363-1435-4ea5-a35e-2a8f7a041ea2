import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { Button } from '../atoms';

interface NarrativeStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  cta?: {
    text: string;
    action: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  visual?: {
    type: 'gradient' | 'image' | 'icon';
    content: string;
    position: 'left' | 'right' | 'center';
  };
}

interface ScrollNarrativeSectionProps {
  className?: string;
}

const ScrollNarrativeSection: React.FC<ScrollNarrativeSectionProps> = ({ className }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  const navigate = useNavigate();

  // Define the narrative steps
  const narrativeSteps: NarrativeStep[] = [
    {
      id: 'vision',
      title: 'Your Vision',
      subtitle: 'Becomes Reality',
      description: 'Every great digital transformation starts with a vision. We take your ideas and turn them into powerful, scalable solutions that drive real business results.',
      cta: {
        text: 'Share Your Vision',
        action: () => navigate('/contact'),
        variant: 'primary',
      },
      visual: {
        type: 'gradient',
        content: 'from-blue-500 to-purple-600',
        position: 'right',
      },
    },
    {
      id: 'development',
      title: 'Full-Stack',
      subtitle: 'Development',
      description: 'From responsive frontends to robust backends, we build complete solutions using cutting-edge technologies like React, TypeScript, and modern cloud infrastructure.',
      cta: {
        text: 'Explore Services',
        action: () => navigate('/services'),
        variant: 'secondary',
      },
      visual: {
        type: 'icon',
        content: 'code',
        position: 'left',
      },
    },
    {
      id: 'automation',
      title: 'Smart',
      subtitle: 'Automation',
      description: 'Streamline your operations with intelligent automation that reduces manual work, eliminates errors, and scales with your business growth.',
      cta: {
        text: 'Learn More',
        action: () => navigate('/services'),
        variant: 'outline',
      },
      visual: {
        type: 'icon',
        content: 'automation',
        position: 'right',
      },
    },
    {
      id: 'results',
      title: 'Measurable',
      subtitle: 'Results',
      description: 'We don\'t just build software – we deliver solutions that provide measurable ROI, improved efficiency, and competitive advantages for your business.',
      cta: {
        text: 'See Our Work',
        action: () => navigate('/services'),
        variant: 'primary',
      },
      visual: {
        type: 'icon',
        content: 'chart',
        position: 'center',
      },
    },
  ];

  // Scroll progress tracking
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const sectionHeight = sectionRef.current.offsetHeight;
      const windowHeight = window.innerHeight;
      
      // Calculate scroll progress through this section
      const scrollTop = -rect.top;
      const progress = Math.max(0, Math.min(1, scrollTop / (sectionHeight - windowHeight)));
      setScrollProgress(progress);

      // Determine active step based on scroll progress
      const stepIndex = Math.floor(progress * narrativeSteps.length);
      setActiveStep(Math.min(stepIndex, narrativeSteps.length - 1));
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, [narrativeSteps.length]);

  const renderVisual = (visual: NarrativeStep['visual']) => {
    if (!visual) return null;

    const iconMap = {
      code: (
        <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      automation: (
        <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      chart: (
        <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    };

    switch (visual.type) {
      case 'gradient':
        return (
          <div className={cn(
            'w-full h-64 rounded-2xl bg-gradient-to-br',
            visual.content,
            'shadow-2xl'
          )} />
        );
      case 'icon':
        return (
          <div className="w-32 h-32 bg-primary-100 rounded-2xl flex items-center justify-center text-primary-600 shadow-xl">
            {iconMap[visual.content as keyof typeof iconMap]}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <section 
      ref={sectionRef}
      className={cn('relative py-32', className)}
      style={{ height: `${narrativeSteps.length * 100}vh` }}
    >
      {/* Progress Indicator */}
      <div className="fixed top-1/2 right-8 transform -translate-y-1/2 z-20">
        <div className="flex flex-col space-y-3">
          {narrativeSteps.map((_, index) => (
            <div
              key={index}
              className={cn(
                'w-3 h-3 rounded-full transition-all duration-300',
                index === activeStep 
                  ? 'bg-primary-600 scale-125' 
                  : 'bg-secondary-300 hover:bg-secondary-400'
              )}
            />
          ))}
        </div>
      </div>

      {/* Sticky Content Container */}
      <div className="sticky top-0 h-screen flex items-center justify-center">
        <div className="container mx-auto px-4">
          {narrativeSteps.map((step, index) => (
            <div
              key={step.id}
              className={cn(
                'absolute inset-0 flex items-center justify-center transition-all duration-1000',
                index === activeStep 
                  ? 'opacity-100 transform translate-y-0' 
                  : 'opacity-0 transform translate-y-8 pointer-events-none'
              )}
            >
              <div className="max-w-6xl mx-auto">
                <div className={cn(
                  'grid grid-cols-1 lg:grid-cols-2 gap-16 items-center',
                  step.visual?.position === 'right' && 'lg:grid-flow-col-dense'
                )}>
                  {/* Content */}
                  <div className={cn(
                    'text-center lg:text-left',
                    step.visual?.position === 'right' && 'lg:col-start-1',
                    step.visual?.position === 'center' && 'lg:col-span-2 text-center'
                  )}>
                    <h2 className="text-5xl md:text-6xl lg:text-7xl font-heading font-bold text-secondary-900 mb-4">
                      {step.title}
                      <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-accent-600">
                        {step.subtitle}
                      </span>
                    </h2>
                    <p className="text-xl md:text-2xl text-secondary-600 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                      {step.description}
                    </p>
                    {step.cta && (
                      <Button
                        variant={step.cta.variant || 'primary'}
                        size="lg"
                        onClick={step.cta.action}
                        className="shadow-lg hover:shadow-xl transition-shadow duration-300"
                      >
                        {step.cta.text}
                      </Button>
                    )}
                  </div>

                  {/* Visual */}
                  {step.visual && step.visual.position !== 'center' && (
                    <div className={cn(
                      'flex justify-center lg:justify-start',
                      step.visual.position === 'right' && 'lg:col-start-2 lg:justify-end'
                    )}>
                      {renderVisual(step.visual)}
                    </div>
                  )}

                  {/* Center visual */}
                  {step.visual && step.visual.position === 'center' && (
                    <div className="flex justify-center lg:col-span-2 mt-8">
                      {renderVisual(step.visual)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div 
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-200 rounded-full opacity-20 blur-3xl"
          style={{
            transform: `translateY(${scrollProgress * 200}px) rotate(${scrollProgress * 360}deg)`,
          }}
        />
        <div 
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-200 rounded-full opacity-15 blur-3xl"
          style={{
            transform: `translateY(${scrollProgress * -150}px) rotate(${scrollProgress * -180}deg)`,
          }}
        />
      </div>
    </section>
  );
};

export default ScrollNarrativeSection;
