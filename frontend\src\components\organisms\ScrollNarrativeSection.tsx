import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { GlassButton, GlassSection } from '../atoms';
import Narrative3DElement from '../atoms/Narrative3DElement';

interface NarrativeStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  cta?: {
    text: string;
    action: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  visual?: {
    type: 'gradient' | 'image' | 'icon' | '3d';
    content: string;
    position: 'left' | 'right' | 'center';
  };
}

interface ScrollNarrativeSectionProps {
  className?: string;
}

const ScrollNarrativeSection: React.FC<ScrollNarrativeSectionProps> = ({ className }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  const navigate = useNavigate();

  // Define the narrative steps
  const narrativeSteps: NarrativeStep[] = [
    {
      id: 'vision',
      title: 'Your Vision',
      subtitle: 'Becomes Reality',
      description: 'Every great digital transformation starts with a vision. We take your ideas and turn them into powerful, scalable solutions that drive real business results.',
      cta: {
        text: 'Share Your Vision',
        action: () => navigate('/contact'),
        variant: 'primary',
      },
    },
    {
      id: 'development',
      title: 'Full-Stack',
      subtitle: 'Development',
      description: 'We deliver enterprise-grade applications with 99.9% uptime, serving over 1M+ users. Our full-stack expertise spans React, Node.js, Python, and cloud infrastructure. Recent projects achieved 300% performance improvements and 40% cost reductions for our clients.',
      cta: {
        text: 'Explore Services',
        action: () => navigate('/services'),
        variant: 'secondary',
      },
    },
    {
      id: 'automation',
      title: 'Smart',
      subtitle: 'Automation',
      description: 'Our automation solutions have saved clients 2,000+ hours monthly and reduced operational costs by 60%. We\'ve implemented 500+ workflows using Make.com, Zapier, and custom APIs, processing over 10M automated tasks with 99.8% accuracy.',
      cta: {
        text: 'Learn More',
        action: () => navigate('/services'),
        variant: 'outline',
      },
    },
    {
      id: 'results',
      title: 'Measurable',
      subtitle: 'Results',
      description: 'Our clients see average ROI of 400% within 6 months. We\'ve helped businesses increase revenue by $50M+ collectively, reduce processing time by 80%, and achieve 95% customer satisfaction scores. Every project includes performance guarantees and success metrics.',
      cta: {
        text: 'See Our Work',
        action: () => navigate('/services'),
        variant: 'primary',
      },
    },
  ];

  // Scroll progress tracking
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const sectionHeight = sectionRef.current.offsetHeight;
      const windowHeight = window.innerHeight;
      
      // Calculate scroll progress through this section
      const scrollTop = -rect.top;
      const progress = Math.max(0, Math.min(1, scrollTop / (sectionHeight - windowHeight)));
      setScrollProgress(progress);

      // Determine active step based on scroll progress
      const stepIndex = Math.floor(progress * narrativeSteps.length);
      setActiveStep(Math.min(stepIndex, narrativeSteps.length - 1));
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, [narrativeSteps.length]);

  const renderVisual = (visual: NarrativeStep['visual']) => {
    if (!visual) return null;

    switch (visual.type) {
      case '3d':
        return (
          <Narrative3DElement
            type={visual.content as 'vision' | 'development' | 'automation' | 'results'}
            isActive={activeStep === narrativeSteps.findIndex(step => step.visual?.content === visual.content)}
            scrollProgress={scrollProgress}
            className="mx-auto"
          />
        );
      case 'gradient':
        return (
          <div className={cn(
            'w-full h-64 rounded-2xl bg-gradient-to-br',
            visual.content,
            'shadow-2xl'
          )} />
        );
      case 'icon': {
        const iconMap = {
          code: (
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
          ),
          automation: (
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          ),
          chart: (
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          ),
        };
        return (
          <div className="w-32 h-32 bg-primary-100 rounded-2xl flex items-center justify-center text-primary-600 shadow-xl">
            {iconMap[visual.content as keyof typeof iconMap]}
          </div>
        );
      }
      default:
        return null;
    }
  };

  return (
    <GlassSection
      variant="dark"
      padding="none"
      particles
      className={cn('relative', className)}
      style={{ height: `${narrativeSteps.length * 60}vh` }}
    >
      <div ref={sectionRef} className="relative py-32 h-full">
      {/* Progress Indicator - Hidden on mobile */}
      <div className="hidden md:block fixed top-1/2 right-4 lg:right-8 transform -translate-y-1/2 z-20">
        <div className="flex flex-col space-y-3">
          {narrativeSteps.map((_, index) => (
            <div
              key={index}
              className={cn(
                'w-3 h-3 rounded-full transition-all duration-300',
                index === activeStep
                  ? 'bg-cyan-400 scale-125 shadow-lg shadow-cyan-400/50'
                  : 'bg-white/30 hover:bg-white/50 border border-white/20'
              )}
            />
          ))}
        </div>
      </div>

      {/* Mobile Progress Bar */}
      <div className="md:hidden fixed top-4 left-4 right-4 z-20">
        <div className="bg-black/50 backdrop-blur-sm rounded-full h-2 overflow-hidden border border-white/20">
          <div
            className="h-full bg-gradient-to-r from-cyan-400 to-teal-400 transition-all duration-300 rounded-full"
            style={{ width: `${((activeStep + 1) / narrativeSteps.length) * 100}%` }}
          />
        </div>
        <div className="text-center mt-2">
          <span className="text-sm font-medium text-white bg-black/50 backdrop-blur-sm px-3 py-1 rounded-full border border-white/20">
            {activeStep + 1} of {narrativeSteps.length}
          </span>
        </div>
      </div>

      {/* Sticky Content Container */}
      <div className="sticky top-0 h-screen flex items-center justify-center">
        <div className="container mx-auto px-4">
          {narrativeSteps.map((step, index) => (
            <div key={step.id} className="absolute inset-0 flex items-center justify-center">
              {(() => {
                // Calculate smooth scroll-driven animation values
                const stepDuration = 1 / narrativeSteps.length;
                const startScroll = index * stepDuration;
                const currentStepScroll = scrollProgress - startScroll;
                const normalizedStepScroll = currentStepScroll / stepDuration;
                const clampedProgress = Math.max(0, Math.min(1, normalizedStepScroll));
                
                // Calculate opacity and position based on scroll progress
                const opacity = Math.max(0, Math.min(1, clampedProgress * 2));
                const translateY = (1 - clampedProgress) * 50;
                
                return (
                  <div className="max-w-6xl mx-auto">
                    <div className={cn(
                      'flex flex-col items-center text-center px-4 sm:px-6 lg:px-0 transition-all duration-500 ease-out'
                    )}
                    style={{
                      opacity,
                      transform: `translateY(${translateY}px)`,
                      pointerEvents: opacity < 0.1 ? 'none' : 'auto'
                    }}>
                      {/* Content */}
                      <div className="text-center max-w-4xl">
                        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-heading font-bold text-white mb-4">
                          <span className="block">{step.title}</span>
                          <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">
                            {step.subtitle}
                          </span>
                        </h2>
                        <p className="text-lg sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 leading-relaxed max-w-4xl mx-auto">
                          {step.description}
                        </p>
                        
                        {/* Trust Indicators for non-vision steps */}
                        {step.id !== 'vision' && (
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8 max-w-3xl mx-auto">
                            {step.id === 'development' && (
                              <>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">99.9%</div>
                                  <div className="text-sm text-gray-300">Uptime Guarantee</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">1M+</div>
                                  <div className="text-sm text-gray-300">Users Served</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">300%</div>
                                  <div className="text-sm text-gray-300">Performance Boost</div>
                                </div>
                              </>
                            )}
                            {step.id === 'automation' && (
                              <>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">2,000+</div>
                                  <div className="text-sm text-gray-300">Hours Saved Monthly</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">500+</div>
                                  <div className="text-sm text-gray-300">Workflows Built</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">99.8%</div>
                                  <div className="text-sm text-gray-300">Accuracy Rate</div>
                                </div>
                              </>
                            )}
                            {step.id === 'results' && (
                              <>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">400%</div>
                                  <div className="text-sm text-gray-300">Average ROI</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">$50M+</div>
                                  <div className="text-sm text-gray-300">Revenue Generated</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20">
                                  <div className="text-2xl font-bold text-cyan-400">95%</div>
                                  <div className="text-sm text-gray-300">Client Satisfaction</div>
                                </div>
                              </>
                            )}
                          </div>
                        )}
                        
                        {step.cta && (
                          <GlassButton
                            variant="accent"
                            size="lg"
                            onClick={step.cta.action}
                            className="transform hover:scale-110 transition-transform duration-300"
                          >
                            {step.cta.text}
                          </GlassButton>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          ))}
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-400/20 rounded-full opacity-20 blur-3xl"
          style={{
            transform: `translateY(${scrollProgress * 200}px) rotate(${scrollProgress * 360}deg)`,
          }}
        />
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-teal-400/15 rounded-full opacity-15 blur-3xl"
          style={{
            transform: `translateY(${scrollProgress * -150}px) rotate(${scrollProgress * -180}deg)`,
          }}
        />
      </div>
      </div>
    </GlassSection>
  );
};

export default ScrollNarrativeSection;