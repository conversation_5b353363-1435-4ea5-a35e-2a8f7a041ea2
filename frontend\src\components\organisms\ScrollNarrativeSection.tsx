import React, { useEffect, useState, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { GlassButton, GlassSection } from '../atoms';
import Narrative3DElement from '../atoms/Narrative3DElement';
import { useUnifiedScroll } from '../../hooks';

interface NarrativeStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  cta?: {
    text: string;
    action: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  visual?: {
    type: 'gradient' | 'image' | 'icon' | '3d';
    content: string;
    position: 'left' | 'right' | 'center';
  };
}

interface ScrollNarrativeSectionProps {
  className?: string;
}

const ScrollNarrativeSection: React.FC<ScrollNarrativeSectionProps> = ({ className }) => {
  const [activeStep, setActiveStep] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  const navigate = useNavigate();
  const { scrollY } = useUnifiedScroll();

  // Define the narrative steps
  const narrativeSteps: NarrativeStep[] = [
    {
      id: 'vision',
      title: 'Your Vision',
      subtitle: 'Becomes Reality',
      description: 'Every great digital transformation starts with a vision. We take your ideas and turn them into powerful, scalable solutions that drive real business results.',
      cta: {
        text: 'Share Your Vision',
        action: () => navigate('/contact'),
        variant: 'primary',
      },
    },
    {
      id: 'development',
      title: 'Full-Stack',
      subtitle: 'Development',
      description: 'We deliver enterprise-grade applications with 99.9% uptime, serving over 1M+ users. Our full-stack expertise spans React, Node.js, Python, and cloud infrastructure. Recent projects achieved 300% performance improvements and 40% cost reductions for our clients.',
      cta: {
        text: 'Explore Services',
        action: () => navigate('/services'),
        variant: 'secondary',
      },
    },
    {
      id: 'automation',
      title: 'Smart',
      subtitle: 'Automation',
      description: 'Our automation solutions have saved clients 2,000+ hours monthly and reduced operational costs by 60%. We\'ve implemented 500+ workflows using Make.com, Zapier, and custom APIs, processing over 10M automated tasks with 99.8% accuracy.',
      cta: {
        text: 'Learn More',
        action: () => navigate('/services'),
        variant: 'outline',
      },
    },
    {
      id: 'results',
      title: 'Measurable',
      subtitle: 'Results',
      description: 'Our clients see average ROI of 400% within 6 months. We\'ve helped businesses increase revenue by $50M+ collectively, reduce processing time by 80%, and achieve 95% customer satisfaction scores. Every project includes performance guarantees and success metrics.',
      cta: {
        text: 'See Our Work',
        action: () => navigate('/services'),
        variant: 'primary',
      },
    },
  ];

  // Calculate scroll progress through this section using memoization for performance
  const scrollProgress = useMemo(() => {
    if (!sectionRef.current) return 0;

    const rect = sectionRef.current.getBoundingClientRect();
    const sectionHeight = sectionRef.current.offsetHeight;
    const windowHeight = window.innerHeight;

    // Calculate scroll progress through this section
    const scrollTop = -rect.top;
    const progress = Math.max(0, Math.min(1, scrollTop / (sectionHeight - windowHeight)));

    return progress;
  }, [scrollY]);

  // Update active step based on scroll progress with smooth transitions
  useEffect(() => {
    const stepIndex = Math.floor(scrollProgress * narrativeSteps.length);
    const newActiveStep = Math.min(stepIndex, narrativeSteps.length - 1);

    // Only update if step actually changed to prevent unnecessary re-renders
    if (newActiveStep !== activeStep) {
      setActiveStep(newActiveStep);
    }
  }, [scrollProgress, narrativeSteps.length, activeStep]);

  const renderVisual = (visual: NarrativeStep['visual']) => {
    if (!visual) return null;

    switch (visual.type) {
      case '3d':
        return (
          <Narrative3DElement
            type={visual.content as 'vision' | 'development' | 'automation' | 'results'}
            isActive={activeStep === narrativeSteps.findIndex(step => step.visual?.content === visual.content)}
            scrollProgress={scrollProgress}
            className="mx-auto"
          />
        );
      case 'gradient':
        return (
          <div className={cn(
            'w-full h-64 rounded-2xl bg-gradient-to-br',
            visual.content,
            'shadow-2xl'
          )} />
        );
      case 'icon': {
        const iconMap = {
          code: (
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
          ),
          automation: (
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          ),
          chart: (
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          ),
        };
        return (
          <div className="w-32 h-32 bg-primary-100 rounded-2xl flex items-center justify-center text-primary-600 shadow-xl">
            {iconMap[visual.content as keyof typeof iconMap]}
          </div>
        );
      }
      default:
        return null;
    }
  };

  return (
    <GlassSection
      variant="dark"
      padding="none"
      particles
      className={cn('relative', className)}
      style={{ height: `${narrativeSteps.length * 60}vh` }}
    >
      <div ref={sectionRef} className="relative py-32 h-full">
      {/* Progress Indicator - Hidden on mobile */}
      <div className="hidden md:block fixed top-1/2 right-4 lg:right-8 transform -translate-y-1/2 z-20">
        <div className="flex flex-col space-y-3">
          {narrativeSteps.map((_, index) => (
            <div
              key={index}
              className={cn(
                'w-3 h-3 rounded-full transition-all duration-300',
                index === activeStep
                  ? 'bg-cyan-400 scale-125 shadow-lg shadow-cyan-400/50'
                  : 'bg-white/30 hover:bg-white/50 border border-white/20'
              )}
            />
          ))}
        </div>
      </div>

      {/* Mobile Progress Bar */}
      <div className="md:hidden fixed top-4 left-4 right-4 z-20">
        <div className="bg-black/50 backdrop-blur-sm rounded-full h-2 overflow-hidden border border-white/20">
          <div
            className="h-full bg-gradient-to-r from-cyan-400 to-teal-400 transition-all duration-300 rounded-full"
            style={{ width: `${((activeStep + 1) / narrativeSteps.length) * 100}%` }}
          />
        </div>
        <div className="text-center mt-2">
          <span className="text-sm font-medium text-white bg-black/50 backdrop-blur-sm px-3 py-1 rounded-full border border-white/20">
            {activeStep + 1} of {narrativeSteps.length}
          </span>
        </div>
      </div>

      {/* Sticky Content Container */}
      <div className="sticky top-0 h-screen flex items-center justify-center">
        <div className="container mx-auto px-4" style={{ willChange: 'transform' }}>
          {narrativeSteps.map((step, index) => (
            <div key={step.id} className="absolute inset-0 flex items-center justify-center">
              {(() => {
                // Enhanced smooth scroll-driven animation values
                const stepDuration = 1 / narrativeSteps.length;
                const startScroll = index * stepDuration;
                const endScroll = (index + 1) * stepDuration;
                const currentStepScroll = scrollProgress - startScroll;
                const normalizedStepScroll = currentStepScroll / stepDuration;

                // Advanced easing functions for cinematic transitions
                const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);
                const easeInOutQuart = (t: number) =>
                  t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2;

                // Calculate smooth visibility with extended transition zones
                let containerOpacity = 0;
                let containerTranslateY = 25;

                if (scrollProgress >= startScroll && scrollProgress <= endScroll) {
                  const progress = Math.max(0, Math.min(1, normalizedStepScroll));

                  if (progress <= 0.75) {
                    // Extended fade in phase (75% of the step duration)
                    const fadeInProgress = progress / 0.75;
                    const easedProgress = easeOutCubic(fadeInProgress);
                    containerOpacity = easedProgress;
                    containerTranslateY = 25 * (1 - easedProgress);
                  } else {
                    // Fade out phase (25% of the step duration)
                    const fadeOutProgress = (progress - 0.75) / 0.25;
                    const easedFadeOut = easeInOutQuart(fadeOutProgress);
                    containerOpacity = 1 - easedFadeOut;
                    containerTranslateY = -15 * easedFadeOut;
                  }
                }

                // Individual element animation calculations for staggered effect
                const getElementAnimation = (elementIndex: number, totalElements: number) => {
                  const staggerDelay = elementIndex * 0.15; // 150ms stagger
                  const adjustedProgress = Math.max(0, (containerOpacity * 1.2) - staggerDelay);
                  const elementProgress = Math.min(1, adjustedProgress);

                  return {
                    opacity: easeOutCubic(elementProgress),
                    translateY: 20 * (1 - easeOutCubic(elementProgress)),
                    scale: 0.98 + (0.02 * easeOutCubic(elementProgress))
                  };
                };
                
                return (
                  <div className="max-w-6xl mx-auto">
                    <div className={cn(
                      'flex flex-col items-center text-center px-4 sm:px-6 lg:px-0'
                    )}
                    style={{
                      opacity: containerOpacity,
                      transform: `translateY(${containerTranslateY}px)`,
                      pointerEvents: containerOpacity < 0.1 ? 'none' : 'auto',
                      willChange: 'opacity, transform',
                      transition: 'none', // Disable CSS transitions for JS-controlled animations
                    }}>
                      {/* Content */}
                      <div className="text-center max-w-4xl">
                        {/* Title with staggered animation */}
                        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-heading font-bold text-white mb-4">
                          <span
                            className="block"
                            style={{
                              ...getElementAnimation(0, 3),
                              transform: `translateY(${getElementAnimation(0, 3).translateY}px) scale(${getElementAnimation(0, 3).scale})`,
                              opacity: getElementAnimation(0, 3).opacity,
                              willChange: 'opacity, transform',
                            }}
                          >
                            {step.title}
                          </span>
                          <span
                            className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400"
                            style={{
                              ...getElementAnimation(1, 3),
                              transform: `translateY(${getElementAnimation(1, 3).translateY}px) scale(${getElementAnimation(1, 3).scale})`,
                              opacity: getElementAnimation(1, 3).opacity,
                              willChange: 'opacity, transform',
                            }}
                          >
                            {step.subtitle}
                          </span>
                        </h2>

                        {/* Description with staggered animation */}
                        <p
                          className="text-lg sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 leading-relaxed max-w-4xl mx-auto"
                          style={{
                            ...getElementAnimation(2, 3),
                            transform: `translateY(${getElementAnimation(2, 3).translateY}px) scale(${getElementAnimation(2, 3).scale})`,
                            opacity: getElementAnimation(2, 3).opacity,
                            willChange: 'opacity, transform',
                          }}
                        >
                          {step.description}
                        </p>
                        
                        {/* Trust Indicators for non-vision steps */}
                        {step.id !== 'vision' && (
                          <div
                            className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8 max-w-3xl mx-auto"
                            style={{
                              ...getElementAnimation(3, 6),
                              transform: `translateY(${getElementAnimation(3, 6).translateY}px)`,
                              opacity: getElementAnimation(3, 6).opacity,
                              willChange: 'opacity, transform',
                            }}
                          >
                            {step.id === 'development' && (
                              <>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(4, 7),
                                    transform: `translateY(${getElementAnimation(4, 7).translateY}px) scale(${getElementAnimation(4, 7).scale})`,
                                    opacity: getElementAnimation(4, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">99.9%</div>
                                  <div className="text-sm text-gray-300">Uptime Guarantee</div>
                                </div>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(5, 7),
                                    transform: `translateY(${getElementAnimation(5, 7).translateY}px) scale(${getElementAnimation(5, 7).scale})`,
                                    opacity: getElementAnimation(5, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">1M+</div>
                                  <div className="text-sm text-gray-300">Users Served</div>
                                </div>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(6, 7),
                                    transform: `translateY(${getElementAnimation(6, 7).translateY}px) scale(${getElementAnimation(6, 7).scale})`,
                                    opacity: getElementAnimation(6, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">300%</div>
                                  <div className="text-sm text-gray-300">Performance Boost</div>
                                </div>
                              </>
                            )}
                            {step.id === 'automation' && (
                              <>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(4, 7),
                                    transform: `translateY(${getElementAnimation(4, 7).translateY}px) scale(${getElementAnimation(4, 7).scale})`,
                                    opacity: getElementAnimation(4, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">2,000+</div>
                                  <div className="text-sm text-gray-300">Hours Saved Monthly</div>
                                </div>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(5, 7),
                                    transform: `translateY(${getElementAnimation(5, 7).translateY}px) scale(${getElementAnimation(5, 7).scale})`,
                                    opacity: getElementAnimation(5, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">500+</div>
                                  <div className="text-sm text-gray-300">Workflows Built</div>
                                </div>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(6, 7),
                                    transform: `translateY(${getElementAnimation(6, 7).translateY}px) scale(${getElementAnimation(6, 7).scale})`,
                                    opacity: getElementAnimation(6, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">99.8%</div>
                                  <div className="text-sm text-gray-300">Accuracy Rate</div>
                                </div>
                              </>
                            )}
                            {step.id === 'results' && (
                              <>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(4, 7),
                                    transform: `translateY(${getElementAnimation(4, 7).translateY}px) scale(${getElementAnimation(4, 7).scale})`,
                                    opacity: getElementAnimation(4, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">400%</div>
                                  <div className="text-sm text-gray-300">Average ROI</div>
                                </div>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(5, 7),
                                    transform: `translateY(${getElementAnimation(5, 7).translateY}px) scale(${getElementAnimation(5, 7).scale})`,
                                    opacity: getElementAnimation(5, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">$50M+</div>
                                  <div className="text-sm text-gray-300">Revenue Generated</div>
                                </div>
                                <div
                                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 shadow-sm border border-white/20 transition-all duration-300 hover:bg-white/15 hover:scale-105"
                                  style={{
                                    ...getElementAnimation(6, 7),
                                    transform: `translateY(${getElementAnimation(6, 7).translateY}px) scale(${getElementAnimation(6, 7).scale})`,
                                    opacity: getElementAnimation(6, 7).opacity,
                                    willChange: 'opacity, transform',
                                  }}
                                >
                                  <div className="text-2xl font-bold text-cyan-400">95%</div>
                                  <div className="text-sm text-gray-300">Client Satisfaction</div>
                                </div>
                              </>
                            )}
                          </div>
                        )}

                        {step.cta && (
                          <div
                            style={{
                              ...getElementAnimation(7, 8),
                              transform: `translateY(${getElementAnimation(7, 8).translateY}px) scale(${getElementAnimation(7, 8).scale})`,
                              opacity: getElementAnimation(7, 8).opacity,
                              willChange: 'opacity, transform',
                            }}
                          >
                            <GlassButton
                              variant="accent"
                              size="lg"
                              onClick={step.cta.action}
                              className="transform hover:scale-110 transition-transform duration-300"
                            >
                              {step.cta.text}
                            </GlassButton>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          ))}
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-400/20 rounded-full opacity-20 blur-3xl"
          style={{
            transform: `translateY(${scrollProgress * 200}px) rotate(${scrollProgress * 360}deg)`,
          }}
        />
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-teal-400/15 rounded-full opacity-15 blur-3xl"
          style={{
            transform: `translateY(${scrollProgress * -150}px) rotate(${scrollProgress * -180}deg)`,
          }}
        />
      </div>
      </div>
    </GlassSection>
  );
};

export default ScrollNarrativeSection;