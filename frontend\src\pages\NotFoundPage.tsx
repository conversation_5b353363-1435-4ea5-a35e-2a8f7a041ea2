import React from 'react';
import { Link } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50">
      <div className="max-w-md mx-auto text-center px-4">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-primary-600 mb-4">404</h1>
          <h2 className="text-3xl font-heading font-bold text-secondary-900 mb-4">
            Page Not Found
          </h2>
          <p className="text-lg text-secondary-600 mb-8">
            Sorry, the page you're looking for doesn't exist or has been moved.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link 
            to="/" 
            className="btn-primary inline-block"
          >
            Go Home
          </Link>
          <div className="text-sm text-secondary-500">
            <p>Or try one of these pages:</p>
            <div className="flex flex-wrap justify-center gap-4 mt-2">
              <Link to="/services" className="text-primary-600 hover:text-primary-700">Services</Link>
              <Link to="/products" className="text-primary-600 hover:text-primary-700">Products</Link>
              <Link to="/about" className="text-primary-600 hover:text-primary-700">About</Link>
              <Link to="/contact" className="text-primary-600 hover:text-primary-700">Contact</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
