import React, { useState } from 'react';
import { cn } from '../../utils';
import { GlassButton, GlassCard, GlassSection } from '../atoms';
import { CaseStudy } from '../../types';

interface CaseStudiesSectionProps {
  className?: string;
}

const CaseStudiesSection: React.FC<CaseStudiesSectionProps> = ({ className }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Sample case studies data - in a real app, this would come from an API
  const caseStudies: CaseStudy[] = [
    {
      id: '1',
      title: 'E-commerce Platform Transformation',
      description: 'Complete rebuild of a legacy e-commerce system with modern React frontend and Node.js backend, resulting in 300% performance improvement.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Stripe'],
      challenges: [
        'Legacy system performance issues',
        'Poor mobile experience',
        'Complex inventory management',
        'Payment processing limitations'
      ],
      solutions: [
        'Modern React.js frontend with responsive design',
        'Microservices architecture with Node.js',
        'Real-time inventory tracking',
        'Integrated payment gateway with Stripe'
      ],
      results: [
        '300% improvement in page load times',
        '150% increase in mobile conversions',
        '99.9% uptime achieved',
        '40% reduction in cart abandonment'
      ],
      slug: 'ecommerce-platform-transformation'
    },
    {
      id: '2',
      title: 'Marketing Automation Workflow',
      description: 'Implemented comprehensive make.com automation system that streamlined lead generation and customer onboarding processes.',
      image: '/api/placeholder/600/400',
      technologies: ['Make.com', 'HubSpot', 'Mailchimp', 'Slack', 'Google Sheets'],
      challenges: [
        'Manual lead qualification process',
        'Disconnected marketing tools',
        'Inconsistent follow-up procedures',
        'Data silos across platforms'
      ],
      solutions: [
        'Automated lead scoring and routing',
        'Integrated CRM and email marketing',
        'Triggered follow-up sequences',
        'Centralized data synchronization'
      ],
      results: [
        '80% reduction in manual tasks',
        '250% increase in lead response time',
        '60% improvement in conversion rates',
        '90% time savings on reporting'
      ],
      slug: 'marketing-automation-workflow'
    },
    {
      id: '3',
      title: 'SaaS Dashboard Application',
      description: 'Built a comprehensive analytics dashboard for a SaaS company with real-time data visualization and user management.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'TypeScript', 'D3.js', 'Express.js', 'MongoDB'],
      challenges: [
        'Complex data visualization requirements',
        'Real-time updates needed',
        'Multi-tenant architecture',
        'Advanced user permissions'
      ],
      solutions: [
        'Interactive charts with D3.js',
        'WebSocket real-time connections',
        'Scalable multi-tenant database design',
        'Role-based access control system'
      ],
      results: [
        '500% increase in user engagement',
        'Real-time data updates achieved',
        '99.95% application uptime',
        '70% reduction in support tickets'
      ],
      slug: 'saas-dashboard-application'
    }
  ];

  const categories = [
    { id: 'all', label: 'All Projects' },
    { id: 'fullstack', label: 'Full-Stack Development' },
    { id: 'automation', label: 'Automation' },
    { id: 'saas', label: 'SaaS Applications' }
  ];

  const filteredCaseStudies = selectedCategory === 'all' 
    ? caseStudies 
    : caseStudies.filter(study => {
        if (selectedCategory === 'fullstack') return study.technologies.some(tech => ['React', 'Node.js', 'TypeScript'].includes(tech));
        if (selectedCategory === 'automation') return study.technologies.includes('Make.com');
        if (selectedCategory === 'saas') return study.title.toLowerCase().includes('saas');
        return true;
      });

  const handleViewProject = (caseStudy: CaseStudy) => {
    // In a real app, this would navigate to a detailed case study page
    console.log('View project:', caseStudy.slug);
  };

  const handleContactUs = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <GlassSection variant="light" padding="xl" particles>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Case <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Studies</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Explore how we've helped businesses transform their operations through innovative
            full-stack development and automation solutions.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                'px-6 py-3 mx-2 mb-4 rounded-lg font-medium transition-all duration-300',
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-secondary-100 text-secondary-700 hover:bg-primary-50 hover:text-primary-600'
              )}
            >
              {category.label}
            </button>
          ))}
        </div>

        {/* Case Studies Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
          {filteredCaseStudies.map((caseStudy) => (
            <div 
              key={caseStudy.id}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group"
            >
              {/* Project Image */}
              <div className="relative h-48 bg-gradient-to-br from-primary-100 to-accent-100 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-accent-500/20" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-primary-700">
                    <svg className="h-16 w-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p className="text-sm font-medium">Project Preview</p>
                  </div>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors">
                  {caseStudy.title}
                </h3>
                <p className="text-secondary-600 mb-4 line-clamp-3">
                  {caseStudy.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {caseStudy.technologies.slice(0, 3).map((tech, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs font-medium"
                    >
                      {tech}
                    </span>
                  ))}
                  {caseStudy.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs font-medium">
                      +{caseStudy.technologies.length - 3} more
                    </span>
                  )}
                </div>

                {/* Key Results */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-secondary-900 mb-2">Key Results:</h4>
                  <ul className="space-y-1">
                    {caseStudy.results.slice(0, 2).map((result, index) => (
                      <li key={index} className="flex items-start text-sm text-secondary-600">
                        <svg className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* View Project Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewProject(caseStudy)}
                  className="w-full"
                >
                  View Project Details
                  <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Call-to-Action */}
        <div className="text-center">
          <div className="bg-secondary-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Create Your Success Story?
            </h3>
            <p className="text-body text-secondary-600 mb-6 max-w-2xl mx-auto">
              Let's discuss how we can help transform your business with our full-stack development 
              and automation expertise. Every great project starts with a conversation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="primary"
                size="lg"
                onClick={handleContactUs}
              >
                Start Your Project
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const servicesSection = document.getElementById('services');
                  if (servicesSection) {
                    servicesSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                View Our Services
              </Button>
            </div>
          </div>
        </div>
      </div>
    </GlassSection>
  );
};

export default CaseStudiesSection;
