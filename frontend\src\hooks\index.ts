import { useState, useEffect, useCallback, useRef } from 'react';
import { debounce, throttle } from '../utils';

// Export performance hooks from separate files
export { usePerformance } from './usePerformance';
export { usePerformanceMonitor } from './usePerformanceMonitor';
export { useResponsive, useBreakpoint, useMediaQuery, useDeviceOptimizations, useResponsiveValue } from './useResponsive';

/**
 * Hook for managing loading states
 */
export function useLoading(initialState: boolean = false) {
  const [isLoading, setIsLoading] = useState(initialState);

  const startLoading = useCallback(() => setIsLoading(true), []);
  const stopLoading = useCallback(() => setIsLoading(false), []);
  const toggleLoading = useCallback(() => setIsLoading(prev => !prev), []);

  return {
    isLoading,
    startLoading,
    stopLoading,
    toggleLoading,
  };
}

/**
 * Hook for managing form states
 */
export function useForm<T extends Record<string, any>>(initialValues: T) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});

  const setValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  }, [errors]);

  const setError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  }, []);

  const setFieldTouched = useCallback((name: keyof T) => {
    setTouched(prev => ({ ...prev, [name]: true }));
  }, []);

  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);

  const isValid = Object.keys(errors).length === 0;

  return {
    values,
    errors,
    touched,
    setValue,
    setError,
    setTouched: setFieldTouched,
    reset,
    isValid,
  };
}

/**
 * Unified scroll hook that manages all scroll-related state
 * Eliminates multiple scroll listeners and provides optimized scroll data
 */
export function useUnifiedScroll() {
  const [scrollData, setScrollData] = useState({
    scrollY: 0,
    scrollDirection: null as 'up' | 'down' | null,
    scrollProgress: 0,
    isScrolling: false,
    hasScrolled: false,
  });

  const lastScrollY = useRef(0);
  const scrollTimeout = useRef<NodeJS.Timeout>();
  const rafId = useRef<number>();

  useEffect(() => {
    const updateScrollData = () => {
      const currentScrollY = window.scrollY;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollProgress = documentHeight > 0 ? currentScrollY / documentHeight : 0;

      // Determine scroll direction with threshold to prevent jitter
      let direction = scrollData.scrollDirection;
      const scrollDelta = currentScrollY - lastScrollY.current;

      if (Math.abs(scrollDelta) > 5) {
        direction = scrollDelta > 0 ? 'down' : 'up';
      }

      setScrollData(prev => ({
        ...prev,
        scrollY: currentScrollY,
        scrollDirection: direction,
        scrollProgress: Math.max(0, Math.min(1, scrollProgress)),
        isScrolling: true,
        hasScrolled: currentScrollY > 10,
      }));

      lastScrollY.current = currentScrollY;

      // Clear scrolling state after scroll ends
      clearTimeout(scrollTimeout.current);
      scrollTimeout.current = setTimeout(() => {
        setScrollData(prev => ({ ...prev, isScrolling: false }));
      }, 150);
    };

    const handleScroll = () => {
      // Use requestAnimationFrame for smooth updates
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      rafId.current = requestAnimationFrame(updateScrollData);
    };

    // Throttled scroll handler for performance
    const throttledScroll = throttle(handleScroll, 16); // ~60fps

    window.addEventListener('scroll', throttledScroll, { passive: true });

    // Initial call
    updateScrollData();

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      clearTimeout(scrollTimeout.current);
    };
  }, []);

  return scrollData;
}

/**
 * Legacy hook for backward compatibility
 * @deprecated Use useUnifiedScroll instead
 */
export function useScrollDirection() {
  const { scrollDirection, scrollY } = useUnifiedScroll();
  return { scrollDirection, scrollY };
}

/**
 * Hook for intersection observer
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true);
      }
    }, options);

    observer.observe(element);

    return () => observer.disconnect();
  }, [elementRef, options, hasIntersected]);

  return { isIntersecting, hasIntersected };
}

/**
 * Hook for window size
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    const debouncedResize = debounce(handleResize, 100);
    window.addEventListener('resize', debouncedResize);

    return () => window.removeEventListener('resize', debouncedResize);
  }, []);

  return windowSize;
}

/**
 * Hook for local storage
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  return [storedValue, setValue] as const;
}

/**
 * Hook for managing async operations
 */
export function useAsync<T, E = string>(
  asyncFunction: () => Promise<T>,
  immediate: boolean = true
) {
  const [status, setStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<E | null>(null);

  const execute = useCallback(async () => {
    setStatus('pending');
    setData(null);
    setError(null);

    try {
      const response = await asyncFunction();
      setData(response);
      setStatus('success');
      return response;
    } catch (error) {
      setError(error as E);
      setStatus('error');
      throw error;
    }
  }, [asyncFunction]);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return {
    execute,
    status,
    data,
    error,
    isLoading: status === 'pending',
    isError: status === 'error',
    isSuccess: status === 'success',
  };
}
