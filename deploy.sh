#!/bin/bash

# AptiBot.com Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

echo "🚀 Starting AptiBot.com deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "git is not installed"
        exit 1
    fi
    
    print_success "All dependencies are installed"
}

# Install frontend dependencies
install_dependencies() {
    print_status "Installing frontend dependencies..."
    cd frontend
    npm ci --production=false
    cd ..
    print_success "Dependencies installed"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    cd frontend
    npm run test:run
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_error "Tests failed"
        exit 1
    fi
    cd ..
}

# Build frontend
build_frontend() {
    print_status "Building frontend for production..."
    cd frontend
    npm run build
    if [ $? -eq 0 ]; then
        print_success "Frontend build completed"
    else
        print_error "Frontend build failed"
        exit 1
    fi
    cd ..
}

# Validate build
validate_build() {
    print_status "Validating build..."
    
    if [ ! -d "frontend/dist" ]; then
        print_error "Build directory not found"
        exit 1
    fi
    
    if [ ! -f "frontend/dist/index.html" ]; then
        print_error "index.html not found in build"
        exit 1
    fi
    
    # Check bundle size
    BUNDLE_SIZE=$(du -sh frontend/dist | cut -f1)
    print_status "Bundle size: $BUNDLE_SIZE"
    
    print_success "Build validation passed"
}

# Deploy to Vercel
deploy_to_vercel() {
    print_status "Deploying to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not found, installing..."
        npm install -g vercel
    fi
    
    # Deploy
    vercel --prod --yes
    
    if [ $? -eq 0 ]; then
        print_success "Deployment to Vercel completed"
    else
        print_error "Deployment failed"
        exit 1
    fi
}

# Run security checks
security_checks() {
    print_status "Running security checks..."
    cd frontend
    npm audit --audit-level=high
    if [ $? -eq 0 ]; then
        print_success "Security checks passed"
    else
        print_warning "Security vulnerabilities found, please review"
    fi
    cd ..
}

# Performance checks
performance_checks() {
    print_status "Running performance checks..."
    cd frontend
    
    # Check bundle size
    if [ -f "dist/assets/index-*.js" ]; then
        JS_SIZE=$(ls -lah dist/assets/index-*.js | awk '{print $5}')
        print_status "Main JS bundle size: $JS_SIZE"
        
        # Warning if bundle is too large
        JS_SIZE_BYTES=$(stat -f%z dist/assets/index-*.js 2>/dev/null || stat -c%s dist/assets/index-*.js)
        if [ $JS_SIZE_BYTES -gt 1048576 ]; then  # 1MB
            print_warning "JS bundle is larger than 1MB, consider code splitting"
        fi
    fi
    
    cd ..
    print_success "Performance checks completed"
}

# Post-deployment verification
post_deployment_verification() {
    print_status "Running post-deployment verification..."
    
    # Wait a moment for deployment to propagate
    sleep 10
    
    # Check if site is accessible
    if command -v curl &> /dev/null; then
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://aptibot.com)
        if [ $HTTP_STATUS -eq 200 ]; then
            print_success "Site is accessible (HTTP $HTTP_STATUS)"
        else
            print_error "Site returned HTTP $HTTP_STATUS"
        fi
    else
        print_warning "curl not available, skipping accessibility check"
    fi
}

# Cleanup
cleanup() {
    print_status "Cleaning up..."
    # Remove any temporary files if needed
    print_success "Cleanup completed"
}

# Main deployment process
main() {
    echo "🌟 AptiBot.com Deployment Script"
    echo "================================"
    
    check_dependencies
    install_dependencies
    security_checks
    run_tests
    build_frontend
    validate_build
    performance_checks
    deploy_to_vercel
    post_deployment_verification
    cleanup
    
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo "🌐 Your site should be live at: https://aptibot.com"
    echo ""
    print_success "All deployment steps completed successfully"
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
