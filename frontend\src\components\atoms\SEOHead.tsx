import React, { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonicalUrl?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'AptiBot - Full-Stack Development & Business Automation Solutions',
  description = 'Transform your business with cutting-edge full-stack web development and intelligent automation solutions. Expert React, TypeScript, and cloud development services.',
  keywords = 'full-stack development, web development, React, TypeScript, automation, business solutions, cloud development, API development, modern web applications',
  image = '/images/og-image.jpg',
  url = 'https://aptibot.com',
  type = 'website',
  author = 'AptiBot Team',
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  noIndex = false,
  canonicalUrl,
}) => {
  const siteTitle = 'AptiBot';
  const fullTitle = title.includes(siteTitle) ? title : `${title} | ${siteTitle}`;
  const currentUrl = canonicalUrl || url;
  const imageUrl = image.startsWith('http') ? image : `${url}${image}`;

  // Structured Data for Organization
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'AptiBot',
    url: 'https://aptibot.com',
    logo: `${url}/images/logo.png`,
    description: 'Full-stack development and business automation solutions provider',
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'customer service',
      email: '<EMAIL>',
      availableLanguage: 'English'
    },
    address: {
      '@type': 'PostalAddress',
      streetAddress: '123 Innovation Drive',
      addressLocality: 'Tech City',
      addressRegion: 'TC',
      postalCode: '12345',
      addressCountry: 'US'
    },
    sameAs: [
      'https://linkedin.com/company/aptibot',
      'https://twitter.com/aptibot',
      'https://github.com/aptibot'
    ]
  };

  // Structured Data for Website
  const websiteSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteTitle,
    url: url,
    description: description,
    publisher: {
      '@type': 'Organization',
      name: 'AptiBot'
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `${url}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string'
    }
  };

  // Structured Data for Services
  const serviceSchema = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: 'Full-Stack Development Services',
    provider: {
      '@type': 'Organization',
      name: 'AptiBot'
    },
    description: 'Comprehensive full-stack web development and business automation solutions',
    serviceType: 'Software Development',
    areaServed: 'Worldwide',
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Development Services',
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Frontend Development',
            description: 'Modern React and TypeScript applications'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Backend Development',
            description: 'Scalable APIs and cloud infrastructure'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Business Automation',
            description: 'Intelligent workflow automation solutions'
          }
        }
      ]
    }
  };

  useEffect(() => {
    // Update document title
    document.title = fullTitle;

    // Update meta tags
    const updateMetaTag = (name: string, content: string, property = false) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Update canonical link
    const updateCanonicalLink = (href: string) => {
      let link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (!link) {
        link = document.createElement('link');
        link.setAttribute('rel', 'canonical');
        document.head.appendChild(link);
      }
      link.setAttribute('href', href);
    };

    // Update structured data
    const updateStructuredData = (id: string, data: object) => {
      let script = document.querySelector(`script[data-schema="${id}"]`) as HTMLScriptElement;
      if (!script) {
        script = document.createElement('script');
        script.setAttribute('type', 'application/ld+json');
        script.setAttribute('data-schema', id);
        document.head.appendChild(script);
      }
      script.textContent = JSON.stringify(data);
    };

    // Basic meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', author);
    updateMetaTag('robots', noIndex ? 'noindex,nofollow' : 'index,follow');

    // Open Graph tags
    updateMetaTag('og:title', fullTitle, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', imageUrl, true);
    updateMetaTag('og:url', currentUrl, true);
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:site_name', siteTitle, true);
    updateMetaTag('og:locale', 'en_US', true);

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', fullTitle);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', imageUrl);
    updateMetaTag('twitter:site', '@aptibot');

    // Canonical URL
    updateCanonicalLink(currentUrl);

    // Structured data
    updateStructuredData('organization', organizationSchema);
    updateStructuredData('website', websiteSchema);
    updateStructuredData('service', serviceSchema);

  }, [fullTitle, description, keywords, author, currentUrl, imageUrl, type, noIndex]);

  return null;
};

export default SEOHead;
