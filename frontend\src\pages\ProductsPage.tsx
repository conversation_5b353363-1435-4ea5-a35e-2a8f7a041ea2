import React, { useState } from 'react';
import { GlassCard, GlassButton, GlassSection, GlassInput, SEOHead } from '../components/atoms';

interface AppOption {
  id: string;
  name: string;
  description: string;
  url: string;
  category: string;
  status: 'active' | 'coming-soon';
  icon: string;
  gradient: string;
}

const ProductsPage: React.FC = () => {
  const [selectedApp, setSelectedApp] = useState<string>('');

  const apps: AppOption[] = [
    {
      id: 'crm-dashboard',
      name: 'CRM Dashboard',
      description: 'Comprehensive customer relationship management system with analytics and automation.',
      url: 'https://crm.aptibot.com',
      category: 'Business Management',
      status: 'active',
      icon: '📊',
      gradient: 'from-blue-500/20 to-cyan-500/20'
    },
    {
      id: 'project-tracker',
      name: 'Project Tracker',
      description: 'Advanced project management tool with team collaboration and progress tracking.',
      url: 'https://projects.aptibot.com',
      category: 'Productivity',
      status: 'active',
      icon: '📋',
      gradient: 'from-emerald-500/20 to-teal-500/20'
    },
    {
      id: 'analytics-suite',
      name: 'Analytics Suite',
      description: 'Business intelligence platform with real-time data visualization and reporting.',
      url: 'https://analytics.aptibot.com',
      category: 'Analytics',
      status: 'coming-soon',
      icon: '📈',
      gradient: 'from-purple-500/20 to-pink-500/20'
    },
    {
      id: 'automation-hub',
      name: 'Automation Hub',
      description: 'Centralized platform for managing and monitoring all your business automations.',
      url: 'https://automation.aptibot.com',
      category: 'Automation',
      status: 'coming-soon',
      icon: '🤖',
      gradient: 'from-orange-500/20 to-red-500/20'
    }
  ];

  const handleAppSelection = (appId: string) => {
    setSelectedApp(appId);
    const app = apps.find(a => a.id === appId);
    if (app && app.status === 'active') {
      // Track analytics event
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'app_selection', {
          event_category: 'products',
          event_label: app.name,
        });
      }
      // Open in new tab
      window.open(app.url, '_blank', 'noopener,noreferrer');
    }
  };

  const activeApps = apps.filter(app => app.status === 'active');
  const comingSoonApps = apps.filter(app => app.status === 'coming-soon');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* SEO Optimization */}
      <SEOHead
        title="Our Products - Business Applications & Tools"
        description="Access our suite of powerful business applications including CRM Dashboard, Project Tracker, Analytics Suite, and Automation Hub. Streamline your operations with our tools."
        keywords="business applications, CRM dashboard, project management, analytics suite, automation hub, business tools, productivity software"
        url="https://aptibot.com/products"
        type="website"
      />

      {/* Glassmorphism Hero Section */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-heading font-bold mb-6 text-white leading-tight">
              Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">Products</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              Access our suite of powerful applications designed to streamline your business operations
              and boost productivity.
            </p>
          </div>
        </div>
      </GlassSection>

      {/* App Selector Section with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Choose Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-cyan-400">Application</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed">
                Select from our collection of business applications to access powerful tools
                that help you work smarter and more efficiently.
              </p>
            </div>

            {/* Glassmorphism Dropdown Selector */}
            <GlassCard variant="primary" size="lg" hover glow>
              <label htmlFor="app-selector" className="block text-lg font-semibold text-white mb-4">
                Select an Application:
              </label>

              <div className="relative">
                <select
                  id="app-selector"
                  value={selectedApp}
                  onChange={(e) => handleAppSelection(e.target.value)}
                  className="w-full px-6 py-4 text-lg rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400 appearance-none cursor-pointer text-white"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                    backdropFilter: 'blur(20px) saturate(180%)',
                    WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                  }}
                >
                  <option value="" style={{ background: '#1f2937', color: 'white' }}>Choose an application...</option>
                  <optgroup label="Available Now" style={{ background: '#1f2937', color: 'white' }}>
                    {activeApps.map((app) => (
                      <option key={app.id} value={app.id} style={{ background: '#1f2937', color: 'white' }}>
                        {app.icon} {app.name} - {app.description}
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="Coming Soon" style={{ background: '#1f2937', color: 'white' }}>
                    {comingSoonApps.map((app) => (
                      <option key={app.id} value={app.id} disabled style={{ background: '#1f2937', color: '#9ca3af' }}>
                        {app.icon} {app.name} - {app.description} (Coming Soon)
                      </option>
                    ))}
                  </optgroup>
                </select>

                {/* Custom dropdown arrow */}
                <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                  <svg className="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              {selectedApp && (
                <GlassCard variant="accent" size="md" className="mt-6">
                  {(() => {
                    const app = apps.find(a => a.id === selectedApp);
                    if (!app) return null;

                    return (
                      <div>
                        <div className="flex items-center space-x-3 mb-3">
                          <span className="text-2xl">{app.icon}</span>
                          <h3 className="text-xl font-semibold text-white">{app.name}</h3>
                        </div>
                        <p className="text-gray-300 mb-4">{app.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-cyan-400/20 text-cyan-300 border border-cyan-400/30">
                            {app.category}
                          </span>
                          {app.status === 'active' ? (
                            <span className="text-emerald-400 font-medium flex items-center space-x-1">
                              <span>✓</span>
                              <span>Available Now</span>
                            </span>
                          ) : (
                            <span className="text-yellow-400 font-medium flex items-center space-x-1">
                              <span>⏳</span>
                              <span>Coming Soon</span>
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </GlassCard>
              )}
            </GlassCard>
          </div>
        </div>
      </GlassSection>

      {/* Available Applications Grid with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-white mb-12 text-center">
              Available <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-cyan-400">Applications</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {activeApps.map((app, index) => (
                <GlassCard
                  key={app.id}
                  variant={index % 2 === 0 ? "primary" : "accent"}
                  size="lg"
                  hover
                  glow
                  className="group"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl transform group-hover:scale-110 transition-transform duration-500">{app.icon}</span>
                      <h3 className="text-xl font-semibold text-white group-hover:text-cyan-300 transition-colors duration-300">{app.name}</h3>
                    </div>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-400/20 text-emerald-300 border border-emerald-400/30">
                      Active
                    </span>
                  </div>
                  <p className="text-gray-300 mb-6 group-hover:text-gray-200 transition-colors duration-300">{app.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-400 bg-white/10 px-3 py-1 rounded-full">{app.category}</span>
                    <GlassButton
                      variant="accent"
                      size="sm"
                      onClick={() => handleAppSelection(app.id)}
                      className="transform hover:scale-110 transition-transform duration-300"
                    >
                      Launch App
                    </GlassButton>
                  </div>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Coming Soon Applications with Glassmorphism */}
      {comingSoonApps.length > 0 && (
        <GlassSection variant="dark" padding="xl">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-heading font-bold text-white mb-12 text-center">
                Coming <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">Soon</span>
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {comingSoonApps.map((app, index) => (
                  <GlassCard
                    key={app.id}
                    variant="secondary"
                    size="lg"
                    hover={false}
                    className="opacity-75 group"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className="text-3xl grayscale">{app.icon}</span>
                        <h3 className="text-xl font-semibold text-white">{app.name}</h3>
                      </div>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-400/20 text-yellow-300 border border-yellow-400/30">
                        Coming Soon
                      </span>
                    </div>
                    <p className="text-gray-300 mb-6">{app.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400 bg-white/10 px-3 py-1 rounded-full">{app.category}</span>
                      <GlassButton
                        variant="outline"
                        size="sm"
                        disabled
                        className="opacity-50 cursor-not-allowed"
                      >
                        Coming Soon
                      </GlassButton>
                    </div>
                  </GlassCard>
                ))}
              </div>
            </div>
          </div>
        </GlassSection>
      )}

      {/* Call to Action with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4 text-center">
          <GlassCard variant="primary" size="xl" hover glow className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
              Need a <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">Custom Solution?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8">
              Don't see what you're looking for? We can build custom applications tailored to your specific business needs.
            </p>
            <GlassButton
              variant="accent"
              size="lg"
              onClick={() => window.location.href = '/contact'}
              className="transform hover:scale-110 transition-transform duration-300"
            >
              Discuss Custom Development
            </GlassButton>
          </GlassCard>
        </div>
      </GlassSection>
    </div>
  );
};

export default ProductsPage;
