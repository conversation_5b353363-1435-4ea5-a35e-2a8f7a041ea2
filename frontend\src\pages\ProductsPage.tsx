import React, { useState } from 'react';

interface AppOption {
  id: string;
  name: string;
  description: string;
  url: string;
  category: string;
  status: 'active' | 'coming-soon';
}

const ProductsPage: React.FC = () => {
  const [selectedApp, setSelectedApp] = useState<string>('');

  const apps: AppOption[] = [
    {
      id: 'crm-dashboard',
      name: 'CRM Dashboard',
      description: 'Comprehensive customer relationship management system with analytics and automation.',
      url: 'https://crm.aptibot.com',
      category: 'Business Management',
      status: 'active'
    },
    {
      id: 'project-tracker',
      name: 'Project Tracker',
      description: 'Advanced project management tool with team collaboration and progress tracking.',
      url: 'https://projects.aptibot.com',
      category: 'Productivity',
      status: 'active'
    },
    {
      id: 'analytics-suite',
      name: 'Analytics Suite',
      description: 'Business intelligence platform with real-time data visualization and reporting.',
      url: 'https://analytics.aptibot.com',
      category: 'Analytics',
      status: 'coming-soon'
    },
    {
      id: 'automation-hub',
      name: 'Automation Hub',
      description: 'Centralized platform for managing and monitoring all your business automations.',
      url: 'https://automation.aptibot.com',
      category: 'Automation',
      status: 'coming-soon'
    }
  ];

  const handleAppSelection = (appId: string) => {
    setSelectedApp(appId);
    const app = apps.find(a => a.id === appId);
    if (app && app.status === 'active') {
      // Track analytics event
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'app_selection', {
          event_category: 'products',
          event_label: app.name,
        });
      }
      // Open in new tab
      window.open(app.url, '_blank', 'noopener,noreferrer');
    }
  };

  const activeApps = apps.filter(app => app.status === 'active');
  const comingSoonApps = apps.filter(app => app.status === 'coming-soon');

  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <section className="section-padding bg-gradient-to-br from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6">
              Our Products
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              Access our suite of powerful applications designed to streamline your business operations 
              and boost productivity.
            </p>
          </div>
        </div>
      </section>

      {/* App Selector Section */}
      <section className="section-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Choose Your Application
              </h2>
              <p className="text-xl text-secondary-600 leading-relaxed">
                Select from our collection of business applications to access powerful tools 
                that help you work smarter and more efficiently.
              </p>
            </div>

            {/* Custom Dropdown Selector */}
            <div className="bg-white rounded-2xl shadow-lg border border-secondary-200 p-8">
              <label htmlFor="app-selector" className="block text-lg font-semibold text-secondary-900 mb-4">
                Select an Application:
              </label>
              
              <div className="relative">
                <select
                  id="app-selector"
                  value={selectedApp}
                  onChange={(e) => handleAppSelection(e.target.value)}
                  className="w-full px-6 py-4 text-lg border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white appearance-none cursor-pointer"
                >
                  <option value="">Choose an application...</option>
                  <optgroup label="Available Now">
                    {activeApps.map((app) => (
                      <option key={app.id} value={app.id}>
                        {app.name} - {app.description}
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="Coming Soon">
                    {comingSoonApps.map((app) => (
                      <option key={app.id} value={app.id} disabled>
                        {app.name} - {app.description} (Coming Soon)
                      </option>
                    ))}
                  </optgroup>
                </select>
                
                {/* Custom dropdown arrow */}
                <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                  <svg className="w-6 h-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              {selectedApp && (
                <div className="mt-6 p-6 bg-primary-50 rounded-lg border border-primary-200">
                  {(() => {
                    const app = apps.find(a => a.id === selectedApp);
                    if (!app) return null;
                    
                    return (
                      <div>
                        <h3 className="text-xl font-semibold text-primary-900 mb-2">{app.name}</h3>
                        <p className="text-primary-700 mb-4">{app.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                            {app.category}
                          </span>
                          {app.status === 'active' ? (
                            <span className="text-green-600 font-medium">✓ Available Now</span>
                          ) : (
                            <span className="text-yellow-600 font-medium">⏳ Coming Soon</span>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Available Applications Grid */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-12 text-center">
              Available Applications
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {activeApps.map((app) => (
                <div key={app.id} className="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-shadow duration-300">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-xl font-semibold text-secondary-900">{app.name}</h3>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                  </div>
                  <p className="text-secondary-600 mb-4">{app.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-500">{app.category}</span>
                    <button
                      onClick={() => handleAppSelection(app.id)}
                      className="btn-primary text-sm"
                    >
                      Launch App
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon Applications */}
      {comingSoonApps.length > 0 && (
        <section className="section-padding">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-12 text-center">
                Coming Soon
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {comingSoonApps.map((app) => (
                  <div key={app.id} className="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 opacity-75">
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-xl font-semibold text-secondary-900">{app.name}</h3>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Coming Soon
                      </span>
                    </div>
                    <p className="text-secondary-600 mb-4">{app.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary-500">{app.category}</span>
                      <button
                        disabled
                        className="btn-secondary opacity-50 cursor-not-allowed text-sm"
                      >
                        Coming Soon
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Call to Action */}
      <section className="section-padding bg-primary-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Need a Custom Solution?
          </h2>
          <p className="text-xl text-primary-100 max-w-2xl mx-auto leading-relaxed mb-8">
            Don't see what you're looking for? We can build custom applications tailored to your specific business needs.
          </p>
          <a 
            href="/contact" 
            className="btn-secondary bg-white text-primary-600 hover:bg-primary-50"
          >
            Discuss Custom Development
          </a>
        </div>
      </section>
    </div>
  );
};

export default ProductsPage;
