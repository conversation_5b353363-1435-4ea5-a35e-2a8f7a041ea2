import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../../test/utils';
import ContactForm from '../ContactForm';
import { mockContactFormData, mockFetchResponse, mockFetchError } from '../../../test/utils';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('ContactForm Component', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  it('renders all form fields', () => {
    render(<ContactForm />);
    
    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/company name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/service type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/budget range/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/project timeline/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/project details/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send message/i })).toBeInTheDocument();
  });

  it('shows validation errors for required fields', async () => {
    render(<ContactForm />);
    
    const submitButton = screen.getByRole('button', { name: /send message/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/please select a service type/i)).toBeInTheDocument();
      expect(screen.getByText(/project details are required/i)).toBeInTheDocument();
      expect(screen.getByText(/please select a budget range/i)).toBeInTheDocument();
      expect(screen.getByText(/please select a timeline/i)).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    render(<ContactForm />);
    
    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.blur(emailInput);
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates minimum character requirements', async () => {
    render(<ContactForm />);
    
    const nameInput = screen.getByLabelText(/full name/i);
    fireEvent.change(nameInput, { target: { value: 'A' } });
    fireEvent.blur(nameInput);
    
    await waitFor(() => {
      expect(screen.getByText(/name must be at least 2 characters/i)).toBeInTheDocument();
    });

    const projectDetails = screen.getByLabelText(/project details/i);
    fireEvent.change(projectDetails, { target: { value: 'Short' } });
    fireEvent.blur(projectDetails);
    
    await waitFor(() => {
      expect(screen.getByText(/please provide more details/i)).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    const mockOnSubmit = vi.fn().mockResolvedValue({});
    mockFetch.mockResolvedValue(mockFetchResponse({ success: true }));
    
    render(<ContactForm onSubmit={mockOnSubmit} />);
    
    // Fill out the form
    fireEvent.change(screen.getByLabelText(/full name/i), { 
      target: { value: mockContactFormData.name } 
    });
    fireEvent.change(screen.getByLabelText(/email address/i), { 
      target: { value: mockContactFormData.email } 
    });
    fireEvent.change(screen.getByLabelText(/company name/i), { 
      target: { value: mockContactFormData.company } 
    });
    
    // Select service type
    fireEvent.change(screen.getByLabelText(/service type/i), { 
      target: { value: mockContactFormData.serviceType } 
    });
    
    // Select budget range
    fireEvent.change(screen.getByLabelText(/budget range/i), { 
      target: { value: mockContactFormData.budgetRange } 
    });
    
    // Select timeline
    fireEvent.change(screen.getByLabelText(/project timeline/i), { 
      target: { value: mockContactFormData.timeline } 
    });
    
    fireEvent.change(screen.getByLabelText(/project details/i), { 
      target: { value: mockContactFormData.projectDetails } 
    });
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /send message/i }));
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(mockContactFormData);
    });
  });

  it('shows loading state during submission', async () => {
    const mockOnSubmit = vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );
    
    render(<ContactForm onSubmit={mockOnSubmit} />);
    
    // Fill required fields
    fireEvent.change(screen.getByLabelText(/full name/i), { 
      target: { value: mockContactFormData.name } 
    });
    fireEvent.change(screen.getByLabelText(/email address/i), { 
      target: { value: mockContactFormData.email } 
    });
    fireEvent.change(screen.getByLabelText(/service type/i), { 
      target: { value: mockContactFormData.serviceType } 
    });
    fireEvent.change(screen.getByLabelText(/budget range/i), { 
      target: { value: mockContactFormData.budgetRange } 
    });
    fireEvent.change(screen.getByLabelText(/project timeline/i), { 
      target: { value: mockContactFormData.timeline } 
    });
    fireEvent.change(screen.getByLabelText(/project details/i), { 
      target: { value: mockContactFormData.projectDetails } 
    });
    
    fireEvent.click(screen.getByRole('button', { name: /send message/i }));
    
    expect(screen.getByText(/sending message/i)).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('shows success message after successful submission', async () => {
    const mockOnSubmit = vi.fn().mockResolvedValue({});
    
    render(<ContactForm onSubmit={mockOnSubmit} />);
    
    // Fill and submit form (abbreviated for brevity)
    fireEvent.change(screen.getByLabelText(/full name/i), { 
      target: { value: mockContactFormData.name } 
    });
    fireEvent.change(screen.getByLabelText(/email address/i), { 
      target: { value: mockContactFormData.email } 
    });
    fireEvent.change(screen.getByLabelText(/service type/i), { 
      target: { value: mockContactFormData.serviceType } 
    });
    fireEvent.change(screen.getByLabelText(/budget range/i), { 
      target: { value: mockContactFormData.budgetRange } 
    });
    fireEvent.change(screen.getByLabelText(/project timeline/i), { 
      target: { value: mockContactFormData.timeline } 
    });
    fireEvent.change(screen.getByLabelText(/project details/i), { 
      target: { value: mockContactFormData.projectDetails } 
    });
    
    fireEvent.click(screen.getByRole('button', { name: /send message/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/thank you! your message has been sent successfully/i)).toBeInTheDocument();
    });
  });

  it('shows error message on submission failure', async () => {
    const mockOnSubmit = vi.fn().mockRejectedValue(new Error('Network error'));
    
    render(<ContactForm onSubmit={mockOnSubmit} />);
    
    // Fill and submit form
    fireEvent.change(screen.getByLabelText(/full name/i), { 
      target: { value: mockContactFormData.name } 
    });
    fireEvent.change(screen.getByLabelText(/email address/i), { 
      target: { value: mockContactFormData.email } 
    });
    fireEvent.change(screen.getByLabelText(/service type/i), { 
      target: { value: mockContactFormData.serviceType } 
    });
    fireEvent.change(screen.getByLabelText(/budget range/i), { 
      target: { value: mockContactFormData.budgetRange } 
    });
    fireEvent.change(screen.getByLabelText(/project timeline/i), { 
      target: { value: mockContactFormData.timeline } 
    });
    fireEvent.change(screen.getByLabelText(/project details/i), { 
      target: { value: mockContactFormData.projectDetails } 
    });
    
    fireEvent.click(screen.getByRole('button', { name: /send message/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/sorry, there was an error sending your message/i)).toBeInTheDocument();
    });
  });

  it('includes honeypot field for bot protection', () => {
    render(<ContactForm />);
    
    const honeypotField = screen.getByDisplayValue('');
    expect(honeypotField).toHaveAttribute('name', 'website');
    expect(honeypotField).toHaveAttribute('tabIndex', '-1');
  });
});
