import React from 'react';
import { cn } from '../../utils';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  glow?: boolean;
  onClick?: () => void;
  style?: React.CSSProperties;
}

const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  hover = true,
  glow = false,
  onClick,
  style,
}) => {
  const baseStyles = {
    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
    backdropFilter: 'blur(20px) saturate(180%)',
    WebkitBackdropFilter: 'blur(20px) saturate(180%)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
  };

  const variantStyles = {
    default: {},
    primary: {
      background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
      border: '1px solid rgba(6, 182, 212, 0.3)',
    },
    secondary: {
      background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%)',
      border: '1px solid rgba(147, 51, 234, 0.3)',
    },
    accent: {
      background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%)',
      border: '1px solid rgba(16, 185, 129, 0.3)',
    },
  };

  const sizeClasses = {
    sm: 'p-4 rounded-lg',
    md: 'p-6 rounded-xl',
    lg: 'p-8 rounded-2xl',
    xl: 'p-10 rounded-3xl',
  };

  const hoverClasses = hover
    ? 'transition-all duration-700 hover:scale-105 hover:-translate-y-2 cursor-pointer'
    : 'transition-all duration-300';

  const glowClasses = glow
    ? 'hover:shadow-2xl hover:shadow-cyan-500/20'
    : '';

  return (
    <div
      className={cn(
        'group relative',
        sizeClasses[size],
        hoverClasses,
        glowClasses,
        onClick && 'cursor-pointer',
        className
      )}
      style={{
        ...baseStyles,
        ...variantStyles[variant],
        ...style,
      }}
      onClick={onClick}
    >
      {/* Glow effect overlay */}
      {glow && (
        <div 
          className="absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100 transition-opacity duration-700"
          style={{
            background: variant === 'primary' 
              ? 'linear-gradient(135deg, rgba(6, 182, 212, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%)'
              : variant === 'secondary'
              ? 'linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(236, 72, 153, 0.2) 100%)'
              : variant === 'accent'
              ? 'linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(6, 182, 212, 0.2) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
            filter: 'blur(1px)',
          }}
        />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Border glow */}
      {hover && (
        <div className="absolute inset-0 rounded-inherit border border-transparent group-hover:border-white/30 transition-all duration-700" />
      )}
    </div>
  );
};

export default GlassCard;
