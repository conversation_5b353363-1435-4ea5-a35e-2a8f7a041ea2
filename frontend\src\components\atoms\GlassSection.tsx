import React from 'react';
import { cn } from '../../utils';

interface GlassSectionProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'dark' | 'light' | 'gradient';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  background?: boolean;
  particles?: boolean;
}

const GlassSection: React.FC<GlassSectionProps> = ({
  children,
  className,
  variant = 'default',
  padding = 'lg',
  background = true,
  particles = false,
}) => {
  const paddingClasses = {
    none: '',
    sm: 'py-8 px-4',
    md: 'py-16 px-6',
    lg: 'py-24 px-8',
    xl: 'py-32 px-12',
  };

  const variantStyles = {
    default: {
      background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%)',
    },
    dark: {
      background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%)',
    },
    light: {
      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
    },
    gradient: {
      background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(147, 51, 234, 0.05) 50%, rgba(16, 185, 129, 0.05) 100%)',
    },
  };

  return (
    <section 
      className={cn(
        'relative overflow-hidden',
        paddingClasses[padding],
        className
      )}
      style={background ? variantStyles[variant] : {}}
    >
      {/* Background particles */}
      {particles && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-cyan-400/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-3/4 right-1/4 w-48 h-48 bg-purple-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-3/4 w-24 h-24 bg-emerald-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </section>
  );
};

export default GlassSection;
