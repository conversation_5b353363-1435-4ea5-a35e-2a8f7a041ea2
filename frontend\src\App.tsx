import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { MainLayout } from './layouts';
import ScrollToTop from './components/utils/ScrollToTop';

// Lazy load page components for optimal performance
const HomePage = lazy(() => import('./pages/HomePage'));
const ServicesPage = lazy(() => import('./pages/ServicesPage'));
const ProductsPage = lazy(() => import('./pages/ProductsPage'));
const AboutPage = lazy(() => import('./pages/AboutPage'));
const ContactPage = lazy(() => import('./pages/ContactPage'));
const NotFoundPage = lazy(() => import('./pages/NotFoundPage'));

// Loading component for lazy-loaded pages
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <p className="text-secondary-600">Loading...</p>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <ScrollToTop />
      <MainLayout>
        <Suspense fallback={<PageLoader />}>
          <Routes>
            {/* Home Page - Immersive Landing Experience */}
            <Route path="/" element={<HomePage />} />

            {/* Services Page - Full-Stack Capabilities */}
            <Route path="/services" element={<ServicesPage />} />

            {/* Products Page - App Hub with Dropdown */}
            <Route path="/products" element={<ProductsPage />} />

            {/* About Page - Company Story & Philosophy */}
            <Route path="/about" element={<AboutPage />} />

            {/* Contact Page - Dedicated Contact Experience */}
            <Route path="/contact" element={<ContactPage />} />

            {/* Catch-all route - 404 Page */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Suspense>
      </MainLayout>
    </Router>
  );
}

export default App;
