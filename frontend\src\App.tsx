import React, { Suspense, lazy } from 'react';
import { MainLayout } from './layouts';
import { HeroSection } from './components/organisms';
import { usePerformance } from './hooks/usePerformance';

// Lazy load non-critical sections
const AboutSection = lazy(() => import('./components/organisms/AboutSection'));
const ServicesSection = lazy(() => import('./components/organisms/ServicesSection'));
const CaseStudiesSection = lazy(() => import('./components/organisms/CaseStudiesSection'));
const ProductsSection = lazy(() => import('./components/organisms/ProductsSection'));
const ContactSection = lazy(() => import('./components/organisms/ContactSection'));

// Loading component for lazy sections
const SectionLoader = () => (
  <div className="section-padding flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
  </div>
);

function App() {
  // Initialize performance monitoring
  usePerformance({
    enableLogging: import.meta.env.DEV,
    enableReporting: import.meta.env.PROD,
    reportingEndpoint: '/api/analytics/performance',
  });

  return (
    <MainLayout>
      <div className="min-h-screen">
        {/* Hero Section - Load immediately for above-the-fold content */}
        <HeroSection />

        {/* Lazy load below-the-fold sections */}
        <Suspense fallback={<SectionLoader />}>
          <AboutSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <ServicesSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <CaseStudiesSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <ProductsSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <ContactSection />
        </Suspense>
      </div>
    </MainLayout>
  );
}

export default App;
