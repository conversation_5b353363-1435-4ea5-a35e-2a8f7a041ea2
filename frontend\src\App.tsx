import React from 'react';
import { MainLayout } from './layouts';

function App() {
  return (
    <MainLayout>
      {/* Temporary content - will be replaced with actual sections */}
      <div className="min-h-screen">
        {/* Hero Section Placeholder */}
        <section id="home" className="section-padding bg-gradient-to-br from-primary-50 to-accent-50">
          <div className="container-custom">
            <div className="text-center">
              <h1 className="heading-xl text-secondary-900 mb-6">
                Welcome to AptiBot
              </h1>
              <p className="text-body text-secondary-600 max-w-2xl mx-auto mb-8">
                Premium full-stack web development and make.com automation solutions.
                We transform your ideas into powerful, scalable digital experiences.
              </p>
            </div>
          </div>
        </section>

        {/* About Section Placeholder */}
        <section id="about" className="section-padding">
          <div className="container-custom">
            <h2 className="heading-lg text-center text-secondary-900 mb-8">About Us</h2>
            <p className="text-body text-secondary-600 text-center max-w-3xl mx-auto">
              AptiBot specializes in delivering cutting-edge full-stack web development solutions
              and powerful automation services using make.com. Our team combines technical expertise
              with creative innovation to build exceptional digital experiences.
            </p>
          </div>
        </section>

        {/* Services Section Placeholder */}
        <section id="services" className="section-padding bg-secondary-50">
          <div className="container-custom">
            <h2 className="heading-lg text-center text-secondary-900 mb-8">Our Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="heading-md text-secondary-900 mb-4">Full-Stack Development</h3>
                <p className="text-body text-secondary-600">
                  Complete web application development from frontend to backend,
                  including databases, APIs, and deployment.
                </p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="heading-md text-secondary-900 mb-4">Make.com Automation</h3>
                <p className="text-body text-secondary-600">
                  Streamline your business processes with powerful automation
                  workflows using make.com platform.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Case Studies Section Placeholder */}
        <section id="case-studies" className="section-padding">
          <div className="container-custom">
            <h2 className="heading-lg text-center text-secondary-900 mb-8">Case Studies</h2>
            <p className="text-body text-secondary-600 text-center">
              Explore our portfolio of successful projects and client transformations.
            </p>
          </div>
        </section>

        {/* Products Section Placeholder */}
        <section id="products" className="section-padding bg-secondary-50">
          <div className="container-custom">
            <h2 className="heading-lg text-center text-secondary-900 mb-8">Our Products</h2>
            <p className="text-body text-secondary-600 text-center">
              Discover our suite of applications and digital solutions.
            </p>
          </div>
        </section>

        {/* Contact Section Placeholder */}
        <section id="contact" className="section-padding">
          <div className="container-custom">
            <h2 className="heading-lg text-center text-secondary-900 mb-8">Contact Us</h2>
            <p className="text-body text-secondary-600 text-center">
              Ready to start your project? Get in touch with our team.
            </p>
          </div>
        </section>
      </div>
    </MainLayout>
  );
}

export default App;
