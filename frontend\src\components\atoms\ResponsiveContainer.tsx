import React from 'react';
import { cn } from '../../utils';
import { useResponsive } from '../../hooks/useResponsive';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  center?: boolean;
  as?: keyof JSX.IntrinsicElements;
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  size = 'lg',
  padding = 'md',
  center = true,
  as: Component = 'div',
}) => {
  const { isMobile, isTablet } = useResponsive();

  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-2 sm:px-4',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-6 sm:px-8 lg:px-12',
    xl: 'px-8 sm:px-12 lg:px-16',
  };

  // Adjust padding for mobile devices
  const mobilePaddingAdjustment = isMobile ? 'px-4' : '';
  const tabletPaddingAdjustment = isTablet ? 'px-6' : '';

  return (
    <Component
      className={cn(
        'w-full',
        sizeClasses[size],
        paddingClasses[padding],
        center && 'mx-auto',
        isMobile && mobilePaddingAdjustment,
        isTablet && tabletPaddingAdjustment,
        className
      )}
    >
      {children}
    </Component>
  );
};

export default ResponsiveContainer;
