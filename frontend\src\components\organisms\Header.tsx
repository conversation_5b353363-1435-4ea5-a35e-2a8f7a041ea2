import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { useScrollDirection } from '../../hooks';
import type { NavItem } from '../../types';
import { Logo, Button } from '../atoms';
import { Navigation, MobileMenu } from '../molecules';

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { scrollDirection, scrollY } = useScrollDirection();

  const navigate = useNavigate();

  // Navigation items updated for multi-page routing
  const navigationItems: NavItem[] = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '/about' },
    { label: 'Services', href: '/services' },
    { label: 'Products', href: '/products' },
    { label: 'Contact', href: '/contact' },
  ];

  // Close mobile menu when window is resized to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileMenuOpen]);

  // Determine if header should be hidden (on scroll down) or shown
  const isHeaderVisible = scrollY < 100 || scrollDirection === 'up';
  
  // Determine if header should have background (when scrolled)
  const hasBackground = scrollY > 50;

  const handleContactClick = () => {
    navigate('/contact');
  };



  return (
    <>
      <header
        className={cn(
          'fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl',
          'bg-black/20 backdrop-blur-xl border border-white/10',
          'shadow-2xl shadow-black/20',
          hasBackground
            ? 'bg-black/40 backdrop-blur-xl border-white/20'
            : 'bg-black/20 backdrop-blur-lg border-white/10',
          isHeaderVisible
            ? 'translate-y-0 opacity-100'
            : '-translate-y-full opacity-0',
          className
        )}
        style={{
          backdropFilter: 'blur(20px) saturate(180%)',
          WebkitBackdropFilter: 'blur(20px) saturate(180%)',
        }}
      >
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 md:h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Logo
                size="md"
                variant={hasBackground ? 'dark' : 'dark'}
                className="cursor-pointer"
                onClick={() => navigate('/')}
              />
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Navigation 
                items={navigationItems}
                variant="horizontal"
              />
              
              <Button
                variant="primary"
                size="md"
                onClick={handleContactClick}
                className="ml-4"
              >
                Get Started
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Open menu"
              >
                <svg
                  className="h-6 w-6 text-secondary-700"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navigationItems={navigationItems}
      />
    </>
  );
};

export default Header;
