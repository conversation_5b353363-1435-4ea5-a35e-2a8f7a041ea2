{"version": 3, "sources": ["../../@splinetool/runtime/build/navmesh.js"], "sourcesContent": ["\nvar Module = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(Module) {\n  Module = Module || {};\n\nvar Module=typeof Module!==\"undefined\"?Module:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!==\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function===\"function\"){var typeNames={\"i\":\"i32\",\"j\":\"i64\",\"f\":\"f32\",\"d\":\"f64\"};var type={parameters:[],results:sig[0]==\"v\"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return new WebAssembly.Function(type,func)}var typeSection=[1,0,1,96];var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={\"i\":127,\"j\":126,\"f\":125,\"d\":124};typeSection.push(sigParam.length);for(var i=0;i<sigParam.length;++i){typeSection.push(typeCodes[sigParam[i]])}if(sigRet==\"v\"){typeSection.push(0)}else{typeSection=typeSection.concat([1,typeCodes[sigRet]])}typeSection[1]=typeSection.length-2;var bytes=new Uint8Array([0,97,115,109,1,0,0,0].concat(typeSection,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0]));var module=new WebAssembly.Module(bytes);var instance=new WebAssembly.Instance(module,{\"e\":{\"f\":func}});var wrappedFunc=instance.exports[\"f\"];return wrappedFunc}var freeTableIndexes=[];var functionsInTableMap;function getEmptyTableSlot(){if(freeTableIndexes.length){return freeTableIndexes.pop()}try{wasmTable.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw\"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.\"}return wasmTable.length-1}function updateTableMap(offset,count){for(var i=offset;i<offset+count;i++){var item=getWasmTableEntry(i);if(item){functionsInTableMap.set(item,i)}}}function addFunction(func,sig){if(!functionsInTableMap){functionsInTableMap=new WeakMap;updateTableMap(0,wasmTable.length)}if(functionsInTableMap.has(func)){return functionsInTableMap.get(func)}var ret=getEmptyTableSlot();try{setWasmTableEntry(ret,func)}catch(err){if(!(err instanceof TypeError)){throw err}var wrapped=convertJsFunctionToWasm(func,sig);setWasmTableEntry(ret,wrapped)}functionsInTableMap.set(func,ret);return ret}var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime=Module[\"noExitRuntime\"]||true;if(typeof WebAssembly!==\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(\"Assertion failed: \"+text)}}var UTF8Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf8\"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str=\"\";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\"}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){{if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -s ASSERTIONS=1 for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile=\"navmesh.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={\"a\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;wasmMemory=Module[\"asm\"][\"m\"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module[\"asm\"][\"Jb\"];addOnInit(Module[\"asm\"][\"n\"]);removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function setWasmTableEntry(idx,func){wasmTable.set(idx,func);wasmTableMirror[idx]=func}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){HEAP32[this.ptr+4>>2]=type};this.get_type=function(){return HEAP32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort(\"\")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var ENV={};function getExecutableName(){return thisProgram||\"./this.program\"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator===\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>\",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+\"=\"+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _fd_close(fd){return 0}function _fd_read(fd,iov,iovcnt,pnum){var stream=SYSCALLS.getStreamFromFD(fd);var num=SYSCALLS.doReadv(stream,iov,iovcnt);HEAP32[pnum>>2]=num;return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov>>2];var len=HEAP32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function __isLeapYear(year){return year%4===0&&(year%100!==0||year%400===0)}function __arraySum(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum}var __MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var __MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function __addDays(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=__isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate}function _strftime(s,maxsize,format,tm){var tm_zone=HEAP32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value===\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}else{return thisDate.getFullYear()}}else{return thisDate.getFullYear()-1}}var EXPANSION_RULES_2={\"%a\":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},\"%A\":function(date){return WEEKDAYS[date.tm_wday]},\"%b\":function(date){return MONTHS[date.tm_mon].substring(0,3)},\"%B\":function(date){return MONTHS[date.tm_mon]},\"%C\":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":function(date){return leadingNulls(date.tm_mday,2)},\"%e\":function(date){return leadingSomething(date.tm_mday,2,\" \")},\"%g\":function(date){return getWeekBasedYear(date).toString().substring(2)},\"%G\":function(date){return getWeekBasedYear(date)},\"%H\":function(date){return leadingNulls(date.tm_hour,2)},\"%I\":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":function(date){return leadingNulls(date.tm_mday+__arraySum(__isLeapYear(date.tm_year+1900)?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,date.tm_mon-1),3)},\"%m\":function(date){return leadingNulls(date.tm_mon+1,2)},\"%M\":function(date){return leadingNulls(date.tm_min,2)},\"%n\":function(){return\"\\n\"},\"%p\":function(date){if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}else{return\"PM\"}},\"%S\":function(date){return leadingNulls(date.tm_sec,2)},\"%t\":function(){return\"\\t\"},\"%u\":function(date){return date.tm_wday||7},\"%U\":function(date){var janFirst=new Date(date.tm_year+1900,0,1);var firstSunday=janFirst.getDay()===0?janFirst:__addDays(janFirst,7-janFirst.getDay());var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstSunday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstSundayUntilEndJanuary=31-firstSunday.getDate();var days=firstSundayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstSunday,janFirst)===0?\"01\":\"00\"},\"%V\":function(date){var janFourthThisYear=new Date(date.tm_year+1900,0,4);var janFourthNextYear=new Date(date.tm_year+1901,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);var endDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);if(compareByDay(endDate,firstWeekStartThisYear)<0){return\"53\"}if(compareByDay(firstWeekStartNextYear,endDate)<=0){return\"01\"}var daysDifference;if(firstWeekStartThisYear.getFullYear()<date.tm_year+1900){daysDifference=date.tm_yday+32-firstWeekStartThisYear.getDate()}else{daysDifference=date.tm_yday+1-firstWeekStartThisYear.getDate()}return leadingNulls(Math.ceil(daysDifference/7),2)},\"%w\":function(date){return date.tm_wday},\"%W\":function(date){var janFirst=new Date(date.tm_year,0,1);var firstMonday=janFirst.getDay()===1?janFirst:__addDays(janFirst,janFirst.getDay()===0?1:7-janFirst.getDay()+1);var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstMonday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstMondayUntilEndJanuary=31-firstMonday.getDate();var days=firstMondayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstMonday,janFirst)===0?\"01\":\"00\"},\"%y\":function(date){return(date.tm_year+1900).toString().substring(2)},\"%Y\":function(date){return date.tm_year+1900},\"%z\":function(date){var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":function(date){return date.tm_zone},\"%%\":function(){return\"%\"}};for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1}function _strftime_l(s,maxsize,format,tm){return _strftime(s,maxsize,format,tm)}function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var asmLibraryArg={\"l\":___cxa_allocate_exception,\"k\":___cxa_throw,\"b\":_abort,\"j\":_emscripten_memcpy_big,\"a\":_emscripten_resize_heap,\"g\":_environ_get,\"h\":_environ_sizes_get,\"c\":_fd_close,\"e\":_fd_read,\"i\":_fd_seek,\"d\":_fd_write,\"f\":_strftime_l};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=function(){return(___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=Module[\"asm\"][\"n\"]).apply(null,arguments)};var _emscripten_bind_VoidPtr___destroy___0=Module[\"_emscripten_bind_VoidPtr___destroy___0\"]=function(){return(_emscripten_bind_VoidPtr___destroy___0=Module[\"_emscripten_bind_VoidPtr___destroy___0\"]=Module[\"asm\"][\"o\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_rcConfig_0=Module[\"_emscripten_bind_rcConfig_rcConfig_0\"]=function(){return(_emscripten_bind_rcConfig_rcConfig_0=Module[\"_emscripten_bind_rcConfig_rcConfig_0\"]=Module[\"asm\"][\"p\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_width_0=Module[\"_emscripten_bind_rcConfig_get_width_0\"]=function(){return(_emscripten_bind_rcConfig_get_width_0=Module[\"_emscripten_bind_rcConfig_get_width_0\"]=Module[\"asm\"][\"q\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_width_1=Module[\"_emscripten_bind_rcConfig_set_width_1\"]=function(){return(_emscripten_bind_rcConfig_set_width_1=Module[\"_emscripten_bind_rcConfig_set_width_1\"]=Module[\"asm\"][\"r\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_height_0=Module[\"_emscripten_bind_rcConfig_get_height_0\"]=function(){return(_emscripten_bind_rcConfig_get_height_0=Module[\"_emscripten_bind_rcConfig_get_height_0\"]=Module[\"asm\"][\"s\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_height_1=Module[\"_emscripten_bind_rcConfig_set_height_1\"]=function(){return(_emscripten_bind_rcConfig_set_height_1=Module[\"_emscripten_bind_rcConfig_set_height_1\"]=Module[\"asm\"][\"t\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_tileSize_0=Module[\"_emscripten_bind_rcConfig_get_tileSize_0\"]=function(){return(_emscripten_bind_rcConfig_get_tileSize_0=Module[\"_emscripten_bind_rcConfig_get_tileSize_0\"]=Module[\"asm\"][\"u\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_tileSize_1=Module[\"_emscripten_bind_rcConfig_set_tileSize_1\"]=function(){return(_emscripten_bind_rcConfig_set_tileSize_1=Module[\"_emscripten_bind_rcConfig_set_tileSize_1\"]=Module[\"asm\"][\"v\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_borderSize_0=Module[\"_emscripten_bind_rcConfig_get_borderSize_0\"]=function(){return(_emscripten_bind_rcConfig_get_borderSize_0=Module[\"_emscripten_bind_rcConfig_get_borderSize_0\"]=Module[\"asm\"][\"w\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_borderSize_1=Module[\"_emscripten_bind_rcConfig_set_borderSize_1\"]=function(){return(_emscripten_bind_rcConfig_set_borderSize_1=Module[\"_emscripten_bind_rcConfig_set_borderSize_1\"]=Module[\"asm\"][\"x\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_cs_0=Module[\"_emscripten_bind_rcConfig_get_cs_0\"]=function(){return(_emscripten_bind_rcConfig_get_cs_0=Module[\"_emscripten_bind_rcConfig_get_cs_0\"]=Module[\"asm\"][\"y\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_cs_1=Module[\"_emscripten_bind_rcConfig_set_cs_1\"]=function(){return(_emscripten_bind_rcConfig_set_cs_1=Module[\"_emscripten_bind_rcConfig_set_cs_1\"]=Module[\"asm\"][\"z\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_ch_0=Module[\"_emscripten_bind_rcConfig_get_ch_0\"]=function(){return(_emscripten_bind_rcConfig_get_ch_0=Module[\"_emscripten_bind_rcConfig_get_ch_0\"]=Module[\"asm\"][\"A\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_ch_1=Module[\"_emscripten_bind_rcConfig_set_ch_1\"]=function(){return(_emscripten_bind_rcConfig_set_ch_1=Module[\"_emscripten_bind_rcConfig_set_ch_1\"]=Module[\"asm\"][\"B\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_bmin_1=Module[\"_emscripten_bind_rcConfig_get_bmin_1\"]=function(){return(_emscripten_bind_rcConfig_get_bmin_1=Module[\"_emscripten_bind_rcConfig_get_bmin_1\"]=Module[\"asm\"][\"C\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_bmin_2=Module[\"_emscripten_bind_rcConfig_set_bmin_2\"]=function(){return(_emscripten_bind_rcConfig_set_bmin_2=Module[\"_emscripten_bind_rcConfig_set_bmin_2\"]=Module[\"asm\"][\"D\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_bmax_1=Module[\"_emscripten_bind_rcConfig_get_bmax_1\"]=function(){return(_emscripten_bind_rcConfig_get_bmax_1=Module[\"_emscripten_bind_rcConfig_get_bmax_1\"]=Module[\"asm\"][\"E\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_bmax_2=Module[\"_emscripten_bind_rcConfig_set_bmax_2\"]=function(){return(_emscripten_bind_rcConfig_set_bmax_2=Module[\"_emscripten_bind_rcConfig_set_bmax_2\"]=Module[\"asm\"][\"F\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableSlopeAngle_0=Module[\"_emscripten_bind_rcConfig_get_walkableSlopeAngle_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableSlopeAngle_0=Module[\"_emscripten_bind_rcConfig_get_walkableSlopeAngle_0\"]=Module[\"asm\"][\"G\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableSlopeAngle_1=Module[\"_emscripten_bind_rcConfig_set_walkableSlopeAngle_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableSlopeAngle_1=Module[\"_emscripten_bind_rcConfig_set_walkableSlopeAngle_1\"]=Module[\"asm\"][\"H\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableHeight_0=Module[\"_emscripten_bind_rcConfig_get_walkableHeight_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableHeight_0=Module[\"_emscripten_bind_rcConfig_get_walkableHeight_0\"]=Module[\"asm\"][\"I\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableHeight_1=Module[\"_emscripten_bind_rcConfig_set_walkableHeight_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableHeight_1=Module[\"_emscripten_bind_rcConfig_set_walkableHeight_1\"]=Module[\"asm\"][\"J\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableClimb_0=Module[\"_emscripten_bind_rcConfig_get_walkableClimb_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableClimb_0=Module[\"_emscripten_bind_rcConfig_get_walkableClimb_0\"]=Module[\"asm\"][\"K\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableClimb_1=Module[\"_emscripten_bind_rcConfig_set_walkableClimb_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableClimb_1=Module[\"_emscripten_bind_rcConfig_set_walkableClimb_1\"]=Module[\"asm\"][\"L\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableRadius_0=Module[\"_emscripten_bind_rcConfig_get_walkableRadius_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableRadius_0=Module[\"_emscripten_bind_rcConfig_get_walkableRadius_0\"]=Module[\"asm\"][\"M\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableRadius_1=Module[\"_emscripten_bind_rcConfig_set_walkableRadius_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableRadius_1=Module[\"_emscripten_bind_rcConfig_set_walkableRadius_1\"]=Module[\"asm\"][\"N\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxEdgeLen_0=Module[\"_emscripten_bind_rcConfig_get_maxEdgeLen_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxEdgeLen_0=Module[\"_emscripten_bind_rcConfig_get_maxEdgeLen_0\"]=Module[\"asm\"][\"O\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxEdgeLen_1=Module[\"_emscripten_bind_rcConfig_set_maxEdgeLen_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxEdgeLen_1=Module[\"_emscripten_bind_rcConfig_set_maxEdgeLen_1\"]=Module[\"asm\"][\"P\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxSimplificationError_0=Module[\"_emscripten_bind_rcConfig_get_maxSimplificationError_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxSimplificationError_0=Module[\"_emscripten_bind_rcConfig_get_maxSimplificationError_0\"]=Module[\"asm\"][\"Q\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxSimplificationError_1=Module[\"_emscripten_bind_rcConfig_set_maxSimplificationError_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxSimplificationError_1=Module[\"_emscripten_bind_rcConfig_set_maxSimplificationError_1\"]=Module[\"asm\"][\"R\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_minRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_minRegionArea_0\"]=function(){return(_emscripten_bind_rcConfig_get_minRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_minRegionArea_0\"]=Module[\"asm\"][\"S\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_minRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_minRegionArea_1\"]=function(){return(_emscripten_bind_rcConfig_set_minRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_minRegionArea_1\"]=Module[\"asm\"][\"T\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_mergeRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_mergeRegionArea_0\"]=function(){return(_emscripten_bind_rcConfig_get_mergeRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_mergeRegionArea_0\"]=Module[\"asm\"][\"U\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_mergeRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_mergeRegionArea_1\"]=function(){return(_emscripten_bind_rcConfig_set_mergeRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_mergeRegionArea_1\"]=Module[\"asm\"][\"V\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxVertsPerPoly_0=Module[\"_emscripten_bind_rcConfig_get_maxVertsPerPoly_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxVertsPerPoly_0=Module[\"_emscripten_bind_rcConfig_get_maxVertsPerPoly_0\"]=Module[\"asm\"][\"W\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxVertsPerPoly_1=Module[\"_emscripten_bind_rcConfig_set_maxVertsPerPoly_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxVertsPerPoly_1=Module[\"_emscripten_bind_rcConfig_set_maxVertsPerPoly_1\"]=Module[\"asm\"][\"X\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_detailSampleDist_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleDist_0\"]=function(){return(_emscripten_bind_rcConfig_get_detailSampleDist_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleDist_0\"]=Module[\"asm\"][\"Y\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_detailSampleDist_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleDist_1\"]=function(){return(_emscripten_bind_rcConfig_set_detailSampleDist_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleDist_1\"]=Module[\"asm\"][\"Z\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_detailSampleMaxError_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleMaxError_0\"]=function(){return(_emscripten_bind_rcConfig_get_detailSampleMaxError_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleMaxError_0\"]=Module[\"asm\"][\"_\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_detailSampleMaxError_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleMaxError_1\"]=function(){return(_emscripten_bind_rcConfig_set_detailSampleMaxError_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleMaxError_1\"]=Module[\"asm\"][\"$\"]).apply(null,arguments)};var _emscripten_bind_rcConfig___destroy___0=Module[\"_emscripten_bind_rcConfig___destroy___0\"]=function(){return(_emscripten_bind_rcConfig___destroy___0=Module[\"_emscripten_bind_rcConfig___destroy___0\"]=Module[\"asm\"][\"aa\"]).apply(null,arguments)};var _emscripten_bind_Vec3_Vec3_0=Module[\"_emscripten_bind_Vec3_Vec3_0\"]=function(){return(_emscripten_bind_Vec3_Vec3_0=Module[\"_emscripten_bind_Vec3_Vec3_0\"]=Module[\"asm\"][\"ba\"]).apply(null,arguments)};var _emscripten_bind_Vec3_Vec3_3=Module[\"_emscripten_bind_Vec3_Vec3_3\"]=function(){return(_emscripten_bind_Vec3_Vec3_3=Module[\"_emscripten_bind_Vec3_Vec3_3\"]=Module[\"asm\"][\"ca\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_x_0=Module[\"_emscripten_bind_Vec3_get_x_0\"]=function(){return(_emscripten_bind_Vec3_get_x_0=Module[\"_emscripten_bind_Vec3_get_x_0\"]=Module[\"asm\"][\"da\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_x_1=Module[\"_emscripten_bind_Vec3_set_x_1\"]=function(){return(_emscripten_bind_Vec3_set_x_1=Module[\"_emscripten_bind_Vec3_set_x_1\"]=Module[\"asm\"][\"ea\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_y_0=Module[\"_emscripten_bind_Vec3_get_y_0\"]=function(){return(_emscripten_bind_Vec3_get_y_0=Module[\"_emscripten_bind_Vec3_get_y_0\"]=Module[\"asm\"][\"fa\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_y_1=Module[\"_emscripten_bind_Vec3_set_y_1\"]=function(){return(_emscripten_bind_Vec3_set_y_1=Module[\"_emscripten_bind_Vec3_set_y_1\"]=Module[\"asm\"][\"ga\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_z_0=Module[\"_emscripten_bind_Vec3_get_z_0\"]=function(){return(_emscripten_bind_Vec3_get_z_0=Module[\"_emscripten_bind_Vec3_get_z_0\"]=Module[\"asm\"][\"ha\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_z_1=Module[\"_emscripten_bind_Vec3_set_z_1\"]=function(){return(_emscripten_bind_Vec3_set_z_1=Module[\"_emscripten_bind_Vec3_set_z_1\"]=Module[\"asm\"][\"ia\"]).apply(null,arguments)};var _emscripten_bind_Vec3___destroy___0=Module[\"_emscripten_bind_Vec3___destroy___0\"]=function(){return(_emscripten_bind_Vec3___destroy___0=Module[\"_emscripten_bind_Vec3___destroy___0\"]=Module[\"asm\"][\"ja\"]).apply(null,arguments)};var _emscripten_bind_Triangle_Triangle_0=Module[\"_emscripten_bind_Triangle_Triangle_0\"]=function(){return(_emscripten_bind_Triangle_Triangle_0=Module[\"_emscripten_bind_Triangle_Triangle_0\"]=Module[\"asm\"][\"ka\"]).apply(null,arguments)};var _emscripten_bind_Triangle_getPoint_1=Module[\"_emscripten_bind_Triangle_getPoint_1\"]=function(){return(_emscripten_bind_Triangle_getPoint_1=Module[\"_emscripten_bind_Triangle_getPoint_1\"]=Module[\"asm\"][\"la\"]).apply(null,arguments)};var _emscripten_bind_Triangle___destroy___0=Module[\"_emscripten_bind_Triangle___destroy___0\"]=function(){return(_emscripten_bind_Triangle___destroy___0=Module[\"_emscripten_bind_Triangle___destroy___0\"]=Module[\"asm\"][\"ma\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_DebugNavMesh_0=Module[\"_emscripten_bind_DebugNavMesh_DebugNavMesh_0\"]=function(){return(_emscripten_bind_DebugNavMesh_DebugNavMesh_0=Module[\"_emscripten_bind_DebugNavMesh_DebugNavMesh_0\"]=Module[\"asm\"][\"na\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_getTriangleCount_0=Module[\"_emscripten_bind_DebugNavMesh_getTriangleCount_0\"]=function(){return(_emscripten_bind_DebugNavMesh_getTriangleCount_0=Module[\"_emscripten_bind_DebugNavMesh_getTriangleCount_0\"]=Module[\"asm\"][\"oa\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_getTriangle_1=Module[\"_emscripten_bind_DebugNavMesh_getTriangle_1\"]=function(){return(_emscripten_bind_DebugNavMesh_getTriangle_1=Module[\"_emscripten_bind_DebugNavMesh_getTriangle_1\"]=Module[\"asm\"][\"pa\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh___destroy___0=Module[\"_emscripten_bind_DebugNavMesh___destroy___0\"]=function(){return(_emscripten_bind_DebugNavMesh___destroy___0=Module[\"_emscripten_bind_DebugNavMesh___destroy___0\"]=Module[\"asm\"][\"qa\"]).apply(null,arguments)};var _emscripten_bind_dtNavMesh___destroy___0=Module[\"_emscripten_bind_dtNavMesh___destroy___0\"]=function(){return(_emscripten_bind_dtNavMesh___destroy___0=Module[\"_emscripten_bind_dtNavMesh___destroy___0\"]=Module[\"asm\"][\"ra\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_NavmeshData_0=Module[\"_emscripten_bind_NavmeshData_NavmeshData_0\"]=function(){return(_emscripten_bind_NavmeshData_NavmeshData_0=Module[\"_emscripten_bind_NavmeshData_NavmeshData_0\"]=Module[\"asm\"][\"sa\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_get_dataPointer_0=Module[\"_emscripten_bind_NavmeshData_get_dataPointer_0\"]=function(){return(_emscripten_bind_NavmeshData_get_dataPointer_0=Module[\"_emscripten_bind_NavmeshData_get_dataPointer_0\"]=Module[\"asm\"][\"ta\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_set_dataPointer_1=Module[\"_emscripten_bind_NavmeshData_set_dataPointer_1\"]=function(){return(_emscripten_bind_NavmeshData_set_dataPointer_1=Module[\"_emscripten_bind_NavmeshData_set_dataPointer_1\"]=Module[\"asm\"][\"ua\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_get_size_0=Module[\"_emscripten_bind_NavmeshData_get_size_0\"]=function(){return(_emscripten_bind_NavmeshData_get_size_0=Module[\"_emscripten_bind_NavmeshData_get_size_0\"]=Module[\"asm\"][\"va\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_set_size_1=Module[\"_emscripten_bind_NavmeshData_set_size_1\"]=function(){return(_emscripten_bind_NavmeshData_set_size_1=Module[\"_emscripten_bind_NavmeshData_set_size_1\"]=Module[\"asm\"][\"wa\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData___destroy___0=Module[\"_emscripten_bind_NavmeshData___destroy___0\"]=function(){return(_emscripten_bind_NavmeshData___destroy___0=Module[\"_emscripten_bind_NavmeshData___destroy___0\"]=Module[\"asm\"][\"xa\"]).apply(null,arguments)};var _emscripten_bind_NavPath_getPointCount_0=Module[\"_emscripten_bind_NavPath_getPointCount_0\"]=function(){return(_emscripten_bind_NavPath_getPointCount_0=Module[\"_emscripten_bind_NavPath_getPointCount_0\"]=Module[\"asm\"][\"ya\"]).apply(null,arguments)};var _emscripten_bind_NavPath_getPoint_1=Module[\"_emscripten_bind_NavPath_getPoint_1\"]=function(){return(_emscripten_bind_NavPath_getPoint_1=Module[\"_emscripten_bind_NavPath_getPoint_1\"]=Module[\"asm\"][\"za\"]).apply(null,arguments)};var _emscripten_bind_NavPath___destroy___0=Module[\"_emscripten_bind_NavPath___destroy___0\"]=function(){return(_emscripten_bind_NavPath___destroy___0=Module[\"_emscripten_bind_NavPath___destroy___0\"]=Module[\"asm\"][\"Aa\"]).apply(null,arguments)};var _emscripten_bind_dtObstacleRef___destroy___0=Module[\"_emscripten_bind_dtObstacleRef___destroy___0\"]=function(){return(_emscripten_bind_dtObstacleRef___destroy___0=Module[\"_emscripten_bind_dtObstacleRef___destroy___0\"]=Module[\"asm\"][\"Ba\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=Module[\"_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=Module[\"_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0\"]=Module[\"asm\"][\"Ca\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_radius_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_radius_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_radius_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_radius_0\"]=Module[\"asm\"][\"Da\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_radius_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_radius_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_radius_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_radius_1\"]=Module[\"asm\"][\"Ea\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_height_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_height_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_height_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_height_0\"]=Module[\"asm\"][\"Fa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_height_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_height_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_height_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_height_1\"]=Module[\"asm\"][\"Ga\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0\"]=Module[\"asm\"][\"Ha\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1\"]=Module[\"asm\"][\"Ia\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0\"]=Module[\"asm\"][\"Ja\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1\"]=Module[\"asm\"][\"Ka\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0\"]=Module[\"asm\"][\"La\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1\"]=Module[\"asm\"][\"Ma\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0\"]=Module[\"asm\"][\"Na\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1\"]=Module[\"asm\"][\"Oa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0\"]=Module[\"asm\"][\"Pa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1\"]=Module[\"asm\"][\"Qa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0\"]=Module[\"asm\"][\"Ra\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1\"]=Module[\"asm\"][\"Sa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0\"]=Module[\"asm\"][\"Ta\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1\"]=Module[\"asm\"][\"Ua\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0\"]=Module[\"asm\"][\"Va\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1\"]=Module[\"asm\"][\"Wa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_userData_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_userData_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_userData_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_userData_0\"]=Module[\"asm\"][\"Xa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_userData_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_userData_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_userData_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_userData_1\"]=Module[\"asm\"][\"Ya\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams___destroy___0=Module[\"_emscripten_bind_dtCrowdAgentParams___destroy___0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams___destroy___0=Module[\"_emscripten_bind_dtCrowdAgentParams___destroy___0\"]=Module[\"asm\"][\"Za\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_NavMesh_0=Module[\"_emscripten_bind_NavMesh_NavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_NavMesh_0=Module[\"_emscripten_bind_NavMesh_NavMesh_0\"]=Module[\"asm\"][\"_a\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_destroy_0=Module[\"_emscripten_bind_NavMesh_destroy_0\"]=function(){return(_emscripten_bind_NavMesh_destroy_0=Module[\"_emscripten_bind_NavMesh_destroy_0\"]=Module[\"asm\"][\"$a\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_build_5=Module[\"_emscripten_bind_NavMesh_build_5\"]=function(){return(_emscripten_bind_NavMesh_build_5=Module[\"_emscripten_bind_NavMesh_build_5\"]=Module[\"asm\"][\"ab\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_buildFromNavmeshData_1=Module[\"_emscripten_bind_NavMesh_buildFromNavmeshData_1\"]=function(){return(_emscripten_bind_NavMesh_buildFromNavmeshData_1=Module[\"_emscripten_bind_NavMesh_buildFromNavmeshData_1\"]=Module[\"asm\"][\"bb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getNavmeshData_0=Module[\"_emscripten_bind_NavMesh_getNavmeshData_0\"]=function(){return(_emscripten_bind_NavMesh_getNavmeshData_0=Module[\"_emscripten_bind_NavMesh_getNavmeshData_0\"]=Module[\"asm\"][\"cb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_freeNavmeshData_1=Module[\"_emscripten_bind_NavMesh_freeNavmeshData_1\"]=function(){return(_emscripten_bind_NavMesh_freeNavmeshData_1=Module[\"_emscripten_bind_NavMesh_freeNavmeshData_1\"]=Module[\"asm\"][\"db\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getDebugNavMesh_0=Module[\"_emscripten_bind_NavMesh_getDebugNavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_getDebugNavMesh_0=Module[\"_emscripten_bind_NavMesh_getDebugNavMesh_0\"]=Module[\"asm\"][\"eb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getClosestPoint_1=Module[\"_emscripten_bind_NavMesh_getClosestPoint_1\"]=function(){return(_emscripten_bind_NavMesh_getClosestPoint_1=Module[\"_emscripten_bind_NavMesh_getClosestPoint_1\"]=Module[\"asm\"][\"fb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getRandomPointAround_2=Module[\"_emscripten_bind_NavMesh_getRandomPointAround_2\"]=function(){return(_emscripten_bind_NavMesh_getRandomPointAround_2=Module[\"_emscripten_bind_NavMesh_getRandomPointAround_2\"]=Module[\"asm\"][\"gb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_moveAlong_2=Module[\"_emscripten_bind_NavMesh_moveAlong_2\"]=function(){return(_emscripten_bind_NavMesh_moveAlong_2=Module[\"_emscripten_bind_NavMesh_moveAlong_2\"]=Module[\"asm\"][\"hb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getNavMesh_0=Module[\"_emscripten_bind_NavMesh_getNavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_getNavMesh_0=Module[\"_emscripten_bind_NavMesh_getNavMesh_0\"]=Module[\"asm\"][\"ib\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_computePath_2=Module[\"_emscripten_bind_NavMesh_computePath_2\"]=function(){return(_emscripten_bind_NavMesh_computePath_2=Module[\"_emscripten_bind_NavMesh_computePath_2\"]=Module[\"asm\"][\"jb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_setDefaultQueryExtent_1=Module[\"_emscripten_bind_NavMesh_setDefaultQueryExtent_1\"]=function(){return(_emscripten_bind_NavMesh_setDefaultQueryExtent_1=Module[\"_emscripten_bind_NavMesh_setDefaultQueryExtent_1\"]=Module[\"asm\"][\"kb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getDefaultQueryExtent_0=Module[\"_emscripten_bind_NavMesh_getDefaultQueryExtent_0\"]=function(){return(_emscripten_bind_NavMesh_getDefaultQueryExtent_0=Module[\"_emscripten_bind_NavMesh_getDefaultQueryExtent_0\"]=Module[\"asm\"][\"lb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_addCylinderObstacle_3=Module[\"_emscripten_bind_NavMesh_addCylinderObstacle_3\"]=function(){return(_emscripten_bind_NavMesh_addCylinderObstacle_3=Module[\"_emscripten_bind_NavMesh_addCylinderObstacle_3\"]=Module[\"asm\"][\"mb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_addBoxObstacle_3=Module[\"_emscripten_bind_NavMesh_addBoxObstacle_3\"]=function(){return(_emscripten_bind_NavMesh_addBoxObstacle_3=Module[\"_emscripten_bind_NavMesh_addBoxObstacle_3\"]=Module[\"asm\"][\"nb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_removeObstacle_1=Module[\"_emscripten_bind_NavMesh_removeObstacle_1\"]=function(){return(_emscripten_bind_NavMesh_removeObstacle_1=Module[\"_emscripten_bind_NavMesh_removeObstacle_1\"]=Module[\"asm\"][\"ob\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_update_0=Module[\"_emscripten_bind_NavMesh_update_0\"]=function(){return(_emscripten_bind_NavMesh_update_0=Module[\"_emscripten_bind_NavMesh_update_0\"]=Module[\"asm\"][\"pb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh___destroy___0=Module[\"_emscripten_bind_NavMesh___destroy___0\"]=function(){return(_emscripten_bind_NavMesh___destroy___0=Module[\"_emscripten_bind_NavMesh___destroy___0\"]=Module[\"asm\"][\"qb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_Crowd_3=Module[\"_emscripten_bind_Crowd_Crowd_3\"]=function(){return(_emscripten_bind_Crowd_Crowd_3=Module[\"_emscripten_bind_Crowd_Crowd_3\"]=Module[\"asm\"][\"rb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_destroy_0=Module[\"_emscripten_bind_Crowd_destroy_0\"]=function(){return(_emscripten_bind_Crowd_destroy_0=Module[\"_emscripten_bind_Crowd_destroy_0\"]=Module[\"asm\"][\"sb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_addAgent_2=Module[\"_emscripten_bind_Crowd_addAgent_2\"]=function(){return(_emscripten_bind_Crowd_addAgent_2=Module[\"_emscripten_bind_Crowd_addAgent_2\"]=Module[\"asm\"][\"tb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_removeAgent_1=Module[\"_emscripten_bind_Crowd_removeAgent_1\"]=function(){return(_emscripten_bind_Crowd_removeAgent_1=Module[\"_emscripten_bind_Crowd_removeAgent_1\"]=Module[\"asm\"][\"ub\"]).apply(null,arguments)};var _emscripten_bind_Crowd_update_1=Module[\"_emscripten_bind_Crowd_update_1\"]=function(){return(_emscripten_bind_Crowd_update_1=Module[\"_emscripten_bind_Crowd_update_1\"]=Module[\"asm\"][\"vb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentPosition_1=Module[\"_emscripten_bind_Crowd_getAgentPosition_1\"]=function(){return(_emscripten_bind_Crowd_getAgentPosition_1=Module[\"_emscripten_bind_Crowd_getAgentPosition_1\"]=Module[\"asm\"][\"wb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentVelocity_1=Module[\"_emscripten_bind_Crowd_getAgentVelocity_1\"]=function(){return(_emscripten_bind_Crowd_getAgentVelocity_1=Module[\"_emscripten_bind_Crowd_getAgentVelocity_1\"]=Module[\"asm\"][\"xb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentNextTargetPath_1=Module[\"_emscripten_bind_Crowd_getAgentNextTargetPath_1\"]=function(){return(_emscripten_bind_Crowd_getAgentNextTargetPath_1=Module[\"_emscripten_bind_Crowd_getAgentNextTargetPath_1\"]=Module[\"asm\"][\"yb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentState_1=Module[\"_emscripten_bind_Crowd_getAgentState_1\"]=function(){return(_emscripten_bind_Crowd_getAgentState_1=Module[\"_emscripten_bind_Crowd_getAgentState_1\"]=Module[\"asm\"][\"zb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_overOffmeshConnection_1=Module[\"_emscripten_bind_Crowd_overOffmeshConnection_1\"]=function(){return(_emscripten_bind_Crowd_overOffmeshConnection_1=Module[\"_emscripten_bind_Crowd_overOffmeshConnection_1\"]=Module[\"asm\"][\"Ab\"]).apply(null,arguments)};var _emscripten_bind_Crowd_agentGoto_2=Module[\"_emscripten_bind_Crowd_agentGoto_2\"]=function(){return(_emscripten_bind_Crowd_agentGoto_2=Module[\"_emscripten_bind_Crowd_agentGoto_2\"]=Module[\"asm\"][\"Bb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_agentTeleport_2=Module[\"_emscripten_bind_Crowd_agentTeleport_2\"]=function(){return(_emscripten_bind_Crowd_agentTeleport_2=Module[\"_emscripten_bind_Crowd_agentTeleport_2\"]=Module[\"asm\"][\"Cb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentParameters_1=Module[\"_emscripten_bind_Crowd_getAgentParameters_1\"]=function(){return(_emscripten_bind_Crowd_getAgentParameters_1=Module[\"_emscripten_bind_Crowd_getAgentParameters_1\"]=Module[\"asm\"][\"Db\"]).apply(null,arguments)};var _emscripten_bind_Crowd_setAgentParameters_2=Module[\"_emscripten_bind_Crowd_setAgentParameters_2\"]=function(){return(_emscripten_bind_Crowd_setAgentParameters_2=Module[\"_emscripten_bind_Crowd_setAgentParameters_2\"]=Module[\"asm\"][\"Eb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_setDefaultQueryExtent_1=Module[\"_emscripten_bind_Crowd_setDefaultQueryExtent_1\"]=function(){return(_emscripten_bind_Crowd_setDefaultQueryExtent_1=Module[\"_emscripten_bind_Crowd_setDefaultQueryExtent_1\"]=Module[\"asm\"][\"Fb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getDefaultQueryExtent_0=Module[\"_emscripten_bind_Crowd_getDefaultQueryExtent_0\"]=function(){return(_emscripten_bind_Crowd_getDefaultQueryExtent_0=Module[\"_emscripten_bind_Crowd_getDefaultQueryExtent_0\"]=Module[\"asm\"][\"Gb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getCorners_1=Module[\"_emscripten_bind_Crowd_getCorners_1\"]=function(){return(_emscripten_bind_Crowd_getCorners_1=Module[\"_emscripten_bind_Crowd_getCorners_1\"]=Module[\"asm\"][\"Hb\"]).apply(null,arguments)};var _emscripten_bind_Crowd___destroy___0=Module[\"_emscripten_bind_Crowd___destroy___0\"]=function(){return(_emscripten_bind_Crowd___destroy___0=Module[\"_emscripten_bind_Crowd___destroy___0\"]=Module[\"asm\"][\"Ib\"]).apply(null,arguments)};var _malloc=Module[\"_malloc\"]=function(){return(_malloc=Module[\"_malloc\"]=Module[\"asm\"][\"Kb\"]).apply(null,arguments)};var _free=Module[\"_free\"]=function(){return(_free=Module[\"_free\"]=Module[\"asm\"][\"Lb\"]).apply(null,arguments)};Module[\"UTF8ToString\"]=UTF8ToString;Module[\"addFunction\"]=addFunction;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}Module[\"run\"]=run;if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();function WrapperObject(){}WrapperObject.prototype=Object.create(WrapperObject.prototype);WrapperObject.prototype.constructor=WrapperObject;WrapperObject.prototype.__class__=WrapperObject;WrapperObject.__cache__={};Module[\"WrapperObject\"]=WrapperObject;function getCache(__class__){return(__class__||WrapperObject).__cache__}Module[\"getCache\"]=getCache;function wrapPointer(ptr,__class__){var cache=getCache(__class__);var ret=cache[ptr];if(ret)return ret;ret=Object.create((__class__||WrapperObject).prototype);ret.ptr=ptr;return cache[ptr]=ret}Module[\"wrapPointer\"]=wrapPointer;function castObject(obj,__class__){return wrapPointer(obj.ptr,__class__)}Module[\"castObject\"]=castObject;Module[\"NULL\"]=wrapPointer(0);function destroy(obj){if(!obj[\"__destroy__\"])throw\"Error: Cannot destroy object. (Did you create it yourself?)\";obj[\"__destroy__\"]();delete getCache(obj.__class__)[obj.ptr]}Module[\"destroy\"]=destroy;function compare(obj1,obj2){return obj1.ptr===obj2.ptr}Module[\"compare\"]=compare;function getPointer(obj){return obj.ptr}Module[\"getPointer\"]=getPointer;function getClass(obj){return obj.__class__}Module[\"getClass\"]=getClass;var ensureCache={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ensureCache.needed){for(var i=0;i<ensureCache.temps.length;i++){Module[\"_free\"](ensureCache.temps[i])}ensureCache.temps.length=0;Module[\"_free\"](ensureCache.buffer);ensureCache.buffer=0;ensureCache.size+=ensureCache.needed;ensureCache.needed=0}if(!ensureCache.buffer){ensureCache.size+=128;ensureCache.buffer=Module[\"_malloc\"](ensureCache.size);assert(ensureCache.buffer)}ensureCache.pos=0},alloc:function(array,view){assert(ensureCache.buffer);var bytes=view.BYTES_PER_ELEMENT;var len=array.length*bytes;len=len+7&-8;var ret;if(ensureCache.pos+len>=ensureCache.size){assert(len>0);ensureCache.needed+=len;ret=Module[\"_malloc\"](len);ensureCache.temps.push(ret)}else{ret=ensureCache.buffer+ensureCache.pos;ensureCache.pos+=len}return ret},copy:function(array,view,offset){offset>>>=0;var bytes=view.BYTES_PER_ELEMENT;switch(bytes){case 2:offset>>>=1;break;case 4:offset>>>=2;break;case 8:offset>>>=3;break}for(var i=0;i<array.length;i++){view[offset+i]=array[i]}}};function ensureInt32(value){if(typeof value===\"object\"){var offset=ensureCache.alloc(value,HEAP32);ensureCache.copy(value,HEAP32,offset);return offset}return value}function ensureFloat32(value){if(typeof value===\"object\"){var offset=ensureCache.alloc(value,HEAPF32);ensureCache.copy(value,HEAPF32,offset);return offset}return value}function VoidPtr(){throw\"cannot construct a VoidPtr, no constructor in IDL\"}VoidPtr.prototype=Object.create(WrapperObject.prototype);VoidPtr.prototype.constructor=VoidPtr;VoidPtr.prototype.__class__=VoidPtr;VoidPtr.__cache__={};Module[\"VoidPtr\"]=VoidPtr;VoidPtr.prototype[\"__destroy__\"]=VoidPtr.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_VoidPtr___destroy___0(self)};function rcConfig(){this.ptr=_emscripten_bind_rcConfig_rcConfig_0();getCache(rcConfig)[this.ptr]=this}rcConfig.prototype=Object.create(WrapperObject.prototype);rcConfig.prototype.constructor=rcConfig;rcConfig.prototype.__class__=rcConfig;rcConfig.__cache__={};Module[\"rcConfig\"]=rcConfig;rcConfig.prototype[\"get_width\"]=rcConfig.prototype.get_width=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_width_0(self)};rcConfig.prototype[\"set_width\"]=rcConfig.prototype.set_width=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_width_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"width\",{get:rcConfig.prototype.get_width,set:rcConfig.prototype.set_width});rcConfig.prototype[\"get_height\"]=rcConfig.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_height_0(self)};rcConfig.prototype[\"set_height\"]=rcConfig.prototype.set_height=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_height_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"height\",{get:rcConfig.prototype.get_height,set:rcConfig.prototype.set_height});rcConfig.prototype[\"get_tileSize\"]=rcConfig.prototype.get_tileSize=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_tileSize_0(self)};rcConfig.prototype[\"set_tileSize\"]=rcConfig.prototype.set_tileSize=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_tileSize_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"tileSize\",{get:rcConfig.prototype.get_tileSize,set:rcConfig.prototype.set_tileSize});rcConfig.prototype[\"get_borderSize\"]=rcConfig.prototype.get_borderSize=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_borderSize_0(self)};rcConfig.prototype[\"set_borderSize\"]=rcConfig.prototype.set_borderSize=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_borderSize_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"borderSize\",{get:rcConfig.prototype.get_borderSize,set:rcConfig.prototype.set_borderSize});rcConfig.prototype[\"get_cs\"]=rcConfig.prototype.get_cs=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_cs_0(self)};rcConfig.prototype[\"set_cs\"]=rcConfig.prototype.set_cs=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_cs_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"cs\",{get:rcConfig.prototype.get_cs,set:rcConfig.prototype.set_cs});rcConfig.prototype[\"get_ch\"]=rcConfig.prototype.get_ch=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_ch_0(self)};rcConfig.prototype[\"set_ch\"]=rcConfig.prototype.set_ch=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_ch_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"ch\",{get:rcConfig.prototype.get_ch,set:rcConfig.prototype.set_ch});rcConfig.prototype[\"get_bmin\"]=rcConfig.prototype.get_bmin=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;return _emscripten_bind_rcConfig_get_bmin_1(self,arg0)};rcConfig.prototype[\"set_bmin\"]=rcConfig.prototype.set_bmin=function(arg0,arg1){var self=this.ptr;ensureCache.prepare();if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;if(arg1&&typeof arg1===\"object\")arg1=arg1.ptr;_emscripten_bind_rcConfig_set_bmin_2(self,arg0,arg1)};Object.defineProperty(rcConfig.prototype,\"bmin\",{get:rcConfig.prototype.get_bmin,set:rcConfig.prototype.set_bmin});rcConfig.prototype[\"get_bmax\"]=rcConfig.prototype.get_bmax=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;return _emscripten_bind_rcConfig_get_bmax_1(self,arg0)};rcConfig.prototype[\"set_bmax\"]=rcConfig.prototype.set_bmax=function(arg0,arg1){var self=this.ptr;ensureCache.prepare();if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;if(arg1&&typeof arg1===\"object\")arg1=arg1.ptr;_emscripten_bind_rcConfig_set_bmax_2(self,arg0,arg1)};Object.defineProperty(rcConfig.prototype,\"bmax\",{get:rcConfig.prototype.get_bmax,set:rcConfig.prototype.set_bmax});rcConfig.prototype[\"get_walkableSlopeAngle\"]=rcConfig.prototype.get_walkableSlopeAngle=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableSlopeAngle_0(self)};rcConfig.prototype[\"set_walkableSlopeAngle\"]=rcConfig.prototype.set_walkableSlopeAngle=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableSlopeAngle_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableSlopeAngle\",{get:rcConfig.prototype.get_walkableSlopeAngle,set:rcConfig.prototype.set_walkableSlopeAngle});rcConfig.prototype[\"get_walkableHeight\"]=rcConfig.prototype.get_walkableHeight=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableHeight_0(self)};rcConfig.prototype[\"set_walkableHeight\"]=rcConfig.prototype.set_walkableHeight=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableHeight_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableHeight\",{get:rcConfig.prototype.get_walkableHeight,set:rcConfig.prototype.set_walkableHeight});rcConfig.prototype[\"get_walkableClimb\"]=rcConfig.prototype.get_walkableClimb=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableClimb_0(self)};rcConfig.prototype[\"set_walkableClimb\"]=rcConfig.prototype.set_walkableClimb=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableClimb_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableClimb\",{get:rcConfig.prototype.get_walkableClimb,set:rcConfig.prototype.set_walkableClimb});rcConfig.prototype[\"get_walkableRadius\"]=rcConfig.prototype.get_walkableRadius=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableRadius_0(self)};rcConfig.prototype[\"set_walkableRadius\"]=rcConfig.prototype.set_walkableRadius=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableRadius_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableRadius\",{get:rcConfig.prototype.get_walkableRadius,set:rcConfig.prototype.set_walkableRadius});rcConfig.prototype[\"get_maxEdgeLen\"]=rcConfig.prototype.get_maxEdgeLen=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxEdgeLen_0(self)};rcConfig.prototype[\"set_maxEdgeLen\"]=rcConfig.prototype.set_maxEdgeLen=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxEdgeLen_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxEdgeLen\",{get:rcConfig.prototype.get_maxEdgeLen,set:rcConfig.prototype.set_maxEdgeLen});rcConfig.prototype[\"get_maxSimplificationError\"]=rcConfig.prototype.get_maxSimplificationError=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxSimplificationError_0(self)};rcConfig.prototype[\"set_maxSimplificationError\"]=rcConfig.prototype.set_maxSimplificationError=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxSimplificationError_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxSimplificationError\",{get:rcConfig.prototype.get_maxSimplificationError,set:rcConfig.prototype.set_maxSimplificationError});rcConfig.prototype[\"get_minRegionArea\"]=rcConfig.prototype.get_minRegionArea=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_minRegionArea_0(self)};rcConfig.prototype[\"set_minRegionArea\"]=rcConfig.prototype.set_minRegionArea=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_minRegionArea_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"minRegionArea\",{get:rcConfig.prototype.get_minRegionArea,set:rcConfig.prototype.set_minRegionArea});rcConfig.prototype[\"get_mergeRegionArea\"]=rcConfig.prototype.get_mergeRegionArea=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_mergeRegionArea_0(self)};rcConfig.prototype[\"set_mergeRegionArea\"]=rcConfig.prototype.set_mergeRegionArea=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_mergeRegionArea_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"mergeRegionArea\",{get:rcConfig.prototype.get_mergeRegionArea,set:rcConfig.prototype.set_mergeRegionArea});rcConfig.prototype[\"get_maxVertsPerPoly\"]=rcConfig.prototype.get_maxVertsPerPoly=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxVertsPerPoly_0(self)};rcConfig.prototype[\"set_maxVertsPerPoly\"]=rcConfig.prototype.set_maxVertsPerPoly=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxVertsPerPoly_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxVertsPerPoly\",{get:rcConfig.prototype.get_maxVertsPerPoly,set:rcConfig.prototype.set_maxVertsPerPoly});rcConfig.prototype[\"get_detailSampleDist\"]=rcConfig.prototype.get_detailSampleDist=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_detailSampleDist_0(self)};rcConfig.prototype[\"set_detailSampleDist\"]=rcConfig.prototype.set_detailSampleDist=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_detailSampleDist_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"detailSampleDist\",{get:rcConfig.prototype.get_detailSampleDist,set:rcConfig.prototype.set_detailSampleDist});rcConfig.prototype[\"get_detailSampleMaxError\"]=rcConfig.prototype.get_detailSampleMaxError=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_detailSampleMaxError_0(self)};rcConfig.prototype[\"set_detailSampleMaxError\"]=rcConfig.prototype.set_detailSampleMaxError=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_detailSampleMaxError_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"detailSampleMaxError\",{get:rcConfig.prototype.get_detailSampleMaxError,set:rcConfig.prototype.set_detailSampleMaxError});rcConfig.prototype[\"__destroy__\"]=rcConfig.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_rcConfig___destroy___0(self)};function Vec3(x,y,z){if(x&&typeof x===\"object\")x=x.ptr;if(y&&typeof y===\"object\")y=y.ptr;if(z&&typeof z===\"object\")z=z.ptr;if(x===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_0();getCache(Vec3)[this.ptr]=this;return}if(y===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_1(x);getCache(Vec3)[this.ptr]=this;return}if(z===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_2(x,y);getCache(Vec3)[this.ptr]=this;return}this.ptr=_emscripten_bind_Vec3_Vec3_3(x,y,z);getCache(Vec3)[this.ptr]=this}Vec3.prototype=Object.create(WrapperObject.prototype);Vec3.prototype.constructor=Vec3;Vec3.prototype.__class__=Vec3;Vec3.__cache__={};Module[\"Vec3\"]=Vec3;Vec3.prototype[\"get_x\"]=Vec3.prototype.get_x=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_x_0(self)};Vec3.prototype[\"set_x\"]=Vec3.prototype.set_x=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_x_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"x\",{get:Vec3.prototype.get_x,set:Vec3.prototype.set_x});Vec3.prototype[\"get_y\"]=Vec3.prototype.get_y=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_y_0(self)};Vec3.prototype[\"set_y\"]=Vec3.prototype.set_y=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_y_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"y\",{get:Vec3.prototype.get_y,set:Vec3.prototype.set_y});Vec3.prototype[\"get_z\"]=Vec3.prototype.get_z=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_z_0(self)};Vec3.prototype[\"set_z\"]=Vec3.prototype.set_z=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_z_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"z\",{get:Vec3.prototype.get_z,set:Vec3.prototype.set_z});Vec3.prototype[\"__destroy__\"]=Vec3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Vec3___destroy___0(self)};function Triangle(){this.ptr=_emscripten_bind_Triangle_Triangle_0();getCache(Triangle)[this.ptr]=this}Triangle.prototype=Object.create(WrapperObject.prototype);Triangle.prototype.constructor=Triangle;Triangle.prototype.__class__=Triangle;Triangle.__cache__={};Module[\"Triangle\"]=Triangle;Triangle.prototype[\"getPoint\"]=Triangle.prototype.getPoint=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_Triangle_getPoint_1(self,n),Vec3)};Triangle.prototype[\"__destroy__\"]=Triangle.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Triangle___destroy___0(self)};function DebugNavMesh(){this.ptr=_emscripten_bind_DebugNavMesh_DebugNavMesh_0();getCache(DebugNavMesh)[this.ptr]=this}DebugNavMesh.prototype=Object.create(WrapperObject.prototype);DebugNavMesh.prototype.constructor=DebugNavMesh;DebugNavMesh.prototype.__class__=DebugNavMesh;DebugNavMesh.__cache__={};Module[\"DebugNavMesh\"]=DebugNavMesh;DebugNavMesh.prototype[\"getTriangleCount\"]=DebugNavMesh.prototype.getTriangleCount=function(){var self=this.ptr;return _emscripten_bind_DebugNavMesh_getTriangleCount_0(self)};DebugNavMesh.prototype[\"getTriangle\"]=DebugNavMesh.prototype.getTriangle=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_DebugNavMesh_getTriangle_1(self,n),Triangle)};DebugNavMesh.prototype[\"__destroy__\"]=DebugNavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DebugNavMesh___destroy___0(self)};function dtNavMesh(){throw\"cannot construct a dtNavMesh, no constructor in IDL\"}dtNavMesh.prototype=Object.create(WrapperObject.prototype);dtNavMesh.prototype.constructor=dtNavMesh;dtNavMesh.prototype.__class__=dtNavMesh;dtNavMesh.__cache__={};Module[\"dtNavMesh\"]=dtNavMesh;dtNavMesh.prototype[\"__destroy__\"]=dtNavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtNavMesh___destroy___0(self)};function NavmeshData(){this.ptr=_emscripten_bind_NavmeshData_NavmeshData_0();getCache(NavmeshData)[this.ptr]=this}NavmeshData.prototype=Object.create(WrapperObject.prototype);NavmeshData.prototype.constructor=NavmeshData;NavmeshData.prototype.__class__=NavmeshData;NavmeshData.__cache__={};Module[\"NavmeshData\"]=NavmeshData;NavmeshData.prototype[\"get_dataPointer\"]=NavmeshData.prototype.get_dataPointer=function(){var self=this.ptr;return _emscripten_bind_NavmeshData_get_dataPointer_0(self)};NavmeshData.prototype[\"set_dataPointer\"]=NavmeshData.prototype.set_dataPointer=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_NavmeshData_set_dataPointer_1(self,arg0)};Object.defineProperty(NavmeshData.prototype,\"dataPointer\",{get:NavmeshData.prototype.get_dataPointer,set:NavmeshData.prototype.set_dataPointer});NavmeshData.prototype[\"get_size\"]=NavmeshData.prototype.get_size=function(){var self=this.ptr;return _emscripten_bind_NavmeshData_get_size_0(self)};NavmeshData.prototype[\"set_size\"]=NavmeshData.prototype.set_size=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_NavmeshData_set_size_1(self,arg0)};Object.defineProperty(NavmeshData.prototype,\"size\",{get:NavmeshData.prototype.get_size,set:NavmeshData.prototype.set_size});NavmeshData.prototype[\"__destroy__\"]=NavmeshData.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavmeshData___destroy___0(self)};function NavPath(){throw\"cannot construct a NavPath, no constructor in IDL\"}NavPath.prototype=Object.create(WrapperObject.prototype);NavPath.prototype.constructor=NavPath;NavPath.prototype.__class__=NavPath;NavPath.__cache__={};Module[\"NavPath\"]=NavPath;NavPath.prototype[\"getPointCount\"]=NavPath.prototype.getPointCount=function(){var self=this.ptr;return _emscripten_bind_NavPath_getPointCount_0(self)};NavPath.prototype[\"getPoint\"]=NavPath.prototype.getPoint=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_NavPath_getPoint_1(self,n),Vec3)};NavPath.prototype[\"__destroy__\"]=NavPath.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavPath___destroy___0(self)};function dtObstacleRef(){throw\"cannot construct a dtObstacleRef, no constructor in IDL\"}dtObstacleRef.prototype=Object.create(WrapperObject.prototype);dtObstacleRef.prototype.constructor=dtObstacleRef;dtObstacleRef.prototype.__class__=dtObstacleRef;dtObstacleRef.__cache__={};Module[\"dtObstacleRef\"]=dtObstacleRef;dtObstacleRef.prototype[\"__destroy__\"]=dtObstacleRef.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtObstacleRef___destroy___0(self)};function dtCrowdAgentParams(){this.ptr=_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0();getCache(dtCrowdAgentParams)[this.ptr]=this}dtCrowdAgentParams.prototype=Object.create(WrapperObject.prototype);dtCrowdAgentParams.prototype.constructor=dtCrowdAgentParams;dtCrowdAgentParams.prototype.__class__=dtCrowdAgentParams;dtCrowdAgentParams.__cache__={};Module[\"dtCrowdAgentParams\"]=dtCrowdAgentParams;dtCrowdAgentParams.prototype[\"get_radius\"]=dtCrowdAgentParams.prototype.get_radius=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_radius_0(self)};dtCrowdAgentParams.prototype[\"set_radius\"]=dtCrowdAgentParams.prototype.set_radius=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_radius_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"radius\",{get:dtCrowdAgentParams.prototype.get_radius,set:dtCrowdAgentParams.prototype.set_radius});dtCrowdAgentParams.prototype[\"get_height\"]=dtCrowdAgentParams.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_height_0(self)};dtCrowdAgentParams.prototype[\"set_height\"]=dtCrowdAgentParams.prototype.set_height=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_height_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"height\",{get:dtCrowdAgentParams.prototype.get_height,set:dtCrowdAgentParams.prototype.set_height});dtCrowdAgentParams.prototype[\"get_maxAcceleration\"]=dtCrowdAgentParams.prototype.get_maxAcceleration=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0(self)};dtCrowdAgentParams.prototype[\"set_maxAcceleration\"]=dtCrowdAgentParams.prototype.set_maxAcceleration=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"maxAcceleration\",{get:dtCrowdAgentParams.prototype.get_maxAcceleration,set:dtCrowdAgentParams.prototype.set_maxAcceleration});dtCrowdAgentParams.prototype[\"get_maxSpeed\"]=dtCrowdAgentParams.prototype.get_maxSpeed=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0(self)};dtCrowdAgentParams.prototype[\"set_maxSpeed\"]=dtCrowdAgentParams.prototype.set_maxSpeed=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"maxSpeed\",{get:dtCrowdAgentParams.prototype.get_maxSpeed,set:dtCrowdAgentParams.prototype.set_maxSpeed});dtCrowdAgentParams.prototype[\"get_collisionQueryRange\"]=dtCrowdAgentParams.prototype.get_collisionQueryRange=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0(self)};dtCrowdAgentParams.prototype[\"set_collisionQueryRange\"]=dtCrowdAgentParams.prototype.set_collisionQueryRange=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"collisionQueryRange\",{get:dtCrowdAgentParams.prototype.get_collisionQueryRange,set:dtCrowdAgentParams.prototype.set_collisionQueryRange});dtCrowdAgentParams.prototype[\"get_pathOptimizationRange\"]=dtCrowdAgentParams.prototype.get_pathOptimizationRange=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0(self)};dtCrowdAgentParams.prototype[\"set_pathOptimizationRange\"]=dtCrowdAgentParams.prototype.set_pathOptimizationRange=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"pathOptimizationRange\",{get:dtCrowdAgentParams.prototype.get_pathOptimizationRange,set:dtCrowdAgentParams.prototype.set_pathOptimizationRange});dtCrowdAgentParams.prototype[\"get_separationWeight\"]=dtCrowdAgentParams.prototype.get_separationWeight=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_separationWeight_0(self)};dtCrowdAgentParams.prototype[\"set_separationWeight\"]=dtCrowdAgentParams.prototype.set_separationWeight=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"separationWeight\",{get:dtCrowdAgentParams.prototype.get_separationWeight,set:dtCrowdAgentParams.prototype.set_separationWeight});dtCrowdAgentParams.prototype[\"get_updateFlags\"]=dtCrowdAgentParams.prototype.get_updateFlags=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_updateFlags_0(self)};dtCrowdAgentParams.prototype[\"set_updateFlags\"]=dtCrowdAgentParams.prototype.set_updateFlags=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"updateFlags\",{get:dtCrowdAgentParams.prototype.get_updateFlags,set:dtCrowdAgentParams.prototype.set_updateFlags});dtCrowdAgentParams.prototype[\"get_obstacleAvoidanceType\"]=dtCrowdAgentParams.prototype.get_obstacleAvoidanceType=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0(self)};dtCrowdAgentParams.prototype[\"set_obstacleAvoidanceType\"]=dtCrowdAgentParams.prototype.set_obstacleAvoidanceType=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"obstacleAvoidanceType\",{get:dtCrowdAgentParams.prototype.get_obstacleAvoidanceType,set:dtCrowdAgentParams.prototype.set_obstacleAvoidanceType});dtCrowdAgentParams.prototype[\"get_queryFilterType\"]=dtCrowdAgentParams.prototype.get_queryFilterType=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0(self)};dtCrowdAgentParams.prototype[\"set_queryFilterType\"]=dtCrowdAgentParams.prototype.set_queryFilterType=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"queryFilterType\",{get:dtCrowdAgentParams.prototype.get_queryFilterType,set:dtCrowdAgentParams.prototype.set_queryFilterType});dtCrowdAgentParams.prototype[\"get_userData\"]=dtCrowdAgentParams.prototype.get_userData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_dtCrowdAgentParams_get_userData_0(self),VoidPtr)};dtCrowdAgentParams.prototype[\"set_userData\"]=dtCrowdAgentParams.prototype.set_userData=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_userData_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"userData\",{get:dtCrowdAgentParams.prototype.get_userData,set:dtCrowdAgentParams.prototype.set_userData});dtCrowdAgentParams.prototype[\"__destroy__\"]=dtCrowdAgentParams.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtCrowdAgentParams___destroy___0(self)};function NavMesh(){this.ptr=_emscripten_bind_NavMesh_NavMesh_0();getCache(NavMesh)[this.ptr]=this}NavMesh.prototype=Object.create(WrapperObject.prototype);NavMesh.prototype.constructor=NavMesh;NavMesh.prototype.__class__=NavMesh;NavMesh.__cache__={};Module[\"NavMesh\"]=NavMesh;NavMesh.prototype[\"destroy\"]=NavMesh.prototype.destroy=function(){var self=this.ptr;_emscripten_bind_NavMesh_destroy_0(self)};NavMesh.prototype[\"build\"]=NavMesh.prototype.build=function(positions,positionCount,indices,indexCount,config){var self=this.ptr;ensureCache.prepare();if(typeof positions==\"object\"){positions=ensureFloat32(positions)}if(positionCount&&typeof positionCount===\"object\")positionCount=positionCount.ptr;if(typeof indices==\"object\"){indices=ensureInt32(indices)}if(indexCount&&typeof indexCount===\"object\")indexCount=indexCount.ptr;if(config&&typeof config===\"object\")config=config.ptr;_emscripten_bind_NavMesh_build_5(self,positions,positionCount,indices,indexCount,config)};NavMesh.prototype[\"buildFromNavmeshData\"]=NavMesh.prototype.buildFromNavmeshData=function(data){var self=this.ptr;if(data&&typeof data===\"object\")data=data.ptr;_emscripten_bind_NavMesh_buildFromNavmeshData_1(self,data)};NavMesh.prototype[\"getNavmeshData\"]=NavMesh.prototype.getNavmeshData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getNavmeshData_0(self),NavmeshData)};NavMesh.prototype[\"freeNavmeshData\"]=NavMesh.prototype.freeNavmeshData=function(data){var self=this.ptr;if(data&&typeof data===\"object\")data=data.ptr;_emscripten_bind_NavMesh_freeNavmeshData_1(self,data)};NavMesh.prototype[\"getDebugNavMesh\"]=NavMesh.prototype.getDebugNavMesh=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getDebugNavMesh_0(self),DebugNavMesh)};NavMesh.prototype[\"getClosestPoint\"]=NavMesh.prototype.getClosestPoint=function(position){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;return wrapPointer(_emscripten_bind_NavMesh_getClosestPoint_1(self,position),Vec3)};NavMesh.prototype[\"getRandomPointAround\"]=NavMesh.prototype.getRandomPointAround=function(position,maxRadius){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(maxRadius&&typeof maxRadius===\"object\")maxRadius=maxRadius.ptr;return wrapPointer(_emscripten_bind_NavMesh_getRandomPointAround_2(self,position,maxRadius),Vec3)};NavMesh.prototype[\"moveAlong\"]=NavMesh.prototype.moveAlong=function(position,destination){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;return wrapPointer(_emscripten_bind_NavMesh_moveAlong_2(self,position,destination),Vec3)};NavMesh.prototype[\"getNavMesh\"]=NavMesh.prototype.getNavMesh=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getNavMesh_0(self),dtNavMesh)};NavMesh.prototype[\"computePath\"]=NavMesh.prototype.computePath=function(start,end){var self=this.ptr;if(start&&typeof start===\"object\")start=start.ptr;if(end&&typeof end===\"object\")end=end.ptr;return wrapPointer(_emscripten_bind_NavMesh_computePath_2(self,start,end),NavPath)};NavMesh.prototype[\"setDefaultQueryExtent\"]=NavMesh.prototype.setDefaultQueryExtent=function(extent){var self=this.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;_emscripten_bind_NavMesh_setDefaultQueryExtent_1(self,extent)};NavMesh.prototype[\"getDefaultQueryExtent\"]=NavMesh.prototype.getDefaultQueryExtent=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getDefaultQueryExtent_0(self),Vec3)};NavMesh.prototype[\"addCylinderObstacle\"]=NavMesh.prototype.addCylinderObstacle=function(position,radius,height){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(radius&&typeof radius===\"object\")radius=radius.ptr;if(height&&typeof height===\"object\")height=height.ptr;return wrapPointer(_emscripten_bind_NavMesh_addCylinderObstacle_3(self,position,radius,height),dtObstacleRef)};NavMesh.prototype[\"addBoxObstacle\"]=NavMesh.prototype.addBoxObstacle=function(position,extent,angle){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;if(angle&&typeof angle===\"object\")angle=angle.ptr;return wrapPointer(_emscripten_bind_NavMesh_addBoxObstacle_3(self,position,extent,angle),dtObstacleRef)};NavMesh.prototype[\"removeObstacle\"]=NavMesh.prototype.removeObstacle=function(obstacle){var self=this.ptr;if(obstacle&&typeof obstacle===\"object\")obstacle=obstacle.ptr;_emscripten_bind_NavMesh_removeObstacle_1(self,obstacle)};NavMesh.prototype[\"update\"]=NavMesh.prototype.update=function(){var self=this.ptr;_emscripten_bind_NavMesh_update_0(self)};NavMesh.prototype[\"__destroy__\"]=NavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavMesh___destroy___0(self)};function Crowd(maxAgents,maxAgentRadius,nav){if(maxAgents&&typeof maxAgents===\"object\")maxAgents=maxAgents.ptr;if(maxAgentRadius&&typeof maxAgentRadius===\"object\")maxAgentRadius=maxAgentRadius.ptr;if(nav&&typeof nav===\"object\")nav=nav.ptr;this.ptr=_emscripten_bind_Crowd_Crowd_3(maxAgents,maxAgentRadius,nav);getCache(Crowd)[this.ptr]=this}Crowd.prototype=Object.create(WrapperObject.prototype);Crowd.prototype.constructor=Crowd;Crowd.prototype.__class__=Crowd;Crowd.__cache__={};Module[\"Crowd\"]=Crowd;Crowd.prototype[\"destroy\"]=Crowd.prototype.destroy=function(){var self=this.ptr;_emscripten_bind_Crowd_destroy_0(self)};Crowd.prototype[\"addAgent\"]=Crowd.prototype.addAgent=function(position,params){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(params&&typeof params===\"object\")params=params.ptr;return _emscripten_bind_Crowd_addAgent_2(self,position,params)};Crowd.prototype[\"removeAgent\"]=Crowd.prototype.removeAgent=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;_emscripten_bind_Crowd_removeAgent_1(self,idx)};Crowd.prototype[\"update\"]=Crowd.prototype.update=function(dt){var self=this.ptr;if(dt&&typeof dt===\"object\")dt=dt.ptr;_emscripten_bind_Crowd_update_1(self,dt)};Crowd.prototype[\"getAgentPosition\"]=Crowd.prototype.getAgentPosition=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentPosition_1(self,idx),Vec3)};Crowd.prototype[\"getAgentVelocity\"]=Crowd.prototype.getAgentVelocity=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentVelocity_1(self,idx),Vec3)};Crowd.prototype[\"getAgentNextTargetPath\"]=Crowd.prototype.getAgentNextTargetPath=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentNextTargetPath_1(self,idx),Vec3)};Crowd.prototype[\"getAgentState\"]=Crowd.prototype.getAgentState=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return _emscripten_bind_Crowd_getAgentState_1(self,idx)};Crowd.prototype[\"overOffmeshConnection\"]=Crowd.prototype.overOffmeshConnection=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return!!_emscripten_bind_Crowd_overOffmeshConnection_1(self,idx)};Crowd.prototype[\"agentGoto\"]=Crowd.prototype.agentGoto=function(idx,destination){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;_emscripten_bind_Crowd_agentGoto_2(self,idx,destination)};Crowd.prototype[\"agentTeleport\"]=Crowd.prototype.agentTeleport=function(idx,destination){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;_emscripten_bind_Crowd_agentTeleport_2(self,idx,destination)};Crowd.prototype[\"getAgentParameters\"]=Crowd.prototype.getAgentParameters=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentParameters_1(self,idx),dtCrowdAgentParams)};Crowd.prototype[\"setAgentParameters\"]=Crowd.prototype.setAgentParameters=function(idx,params){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(params&&typeof params===\"object\")params=params.ptr;_emscripten_bind_Crowd_setAgentParameters_2(self,idx,params)};Crowd.prototype[\"setDefaultQueryExtent\"]=Crowd.prototype.setDefaultQueryExtent=function(extent){var self=this.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;_emscripten_bind_Crowd_setDefaultQueryExtent_1(self,extent)};Crowd.prototype[\"getDefaultQueryExtent\"]=Crowd.prototype.getDefaultQueryExtent=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_Crowd_getDefaultQueryExtent_0(self),Vec3)};Crowd.prototype[\"getCorners\"]=Crowd.prototype.getCorners=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getCorners_1(self,idx),NavPath)};Crowd.prototype[\"__destroy__\"]=Crowd.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Crowd___destroy___0(self)};\n\n\n  return Module.ready\n}\n);\n})();\nexport default Module;"], "mappings": ";;;AACA,IAAI,SAAU,WAAW;AACvB,MAAI,aAAa,OAAO,aAAa,eAAe,SAAS,gBAAgB,SAAS,cAAc,MAAM;AAE1G,SACF,SAASA,SAAQ;AACf,IAAAA,UAASA,WAAU,CAAC;AAEtB,QAAIA,UAAO,OAAOA,YAAS,cAAYA,UAAO,CAAC;AAAE,QAAI,qBAAoB;AAAmB,IAAAA,QAAO,OAAO,IAAE,IAAI,QAAQ,SAAS,SAAQ,QAAO;AAAC,4BAAoB;AAAQ,2BAAmB;AAAA,IAAM,CAAC;AAAE,QAAI,kBAAgB,CAAC;AAAE,QAAI;AAAI,SAAI,OAAOA,SAAO;AAAC,UAAGA,QAAO,eAAe,GAAG,GAAE;AAAC,wBAAgB,GAAG,IAAEA,QAAO,GAAG;AAAA,MAAC;AAAA,IAAC;AAAC,QAAI,aAAW,CAAC;AAAE,QAAI,cAAY;AAAiB,QAAI,QAAM,SAAS,QAAO,SAAQ;AAAC,YAAM;AAAA,IAAO;AAAE,QAAI,qBAAmB;AAAK,QAAI,wBAAsB;AAAM,QAAI,kBAAgB;AAAG,aAAS,WAAW,MAAK;AAAC,UAAGA,QAAO,YAAY,GAAE;AAAC,eAAOA,QAAO,YAAY,EAAE,MAAK,eAAe;AAAA,MAAC;AAAC,aAAO,kBAAgB;AAAA,IAAI;AAAC,QAAI,OAAM,WAAU,YAAW;AAAe,QAAG,sBAAoB,uBAAsB;AAAC,UAAG,uBAAsB;AAAC,0BAAgB,KAAK,SAAS;AAAA,MAAI,WAAS,OAAO,aAAW,eAAa,SAAS,eAAc;AAAC,0BAAgB,SAAS,cAAc;AAAA,MAAG;AAAC,UAAG,YAAW;AAAC,0BAAgB;AAAA,MAAU;AAAC,UAAG,gBAAgB,QAAQ,OAAO,MAAI,GAAE;AAAC,0BAAgB,gBAAgB,OAAO,GAAE,gBAAgB,QAAQ,UAAS,EAAE,EAAE,YAAY,GAAG,IAAE,CAAC;AAAA,MAAC,OAAK;AAAC,0BAAgB;AAAA,MAAE;AAAC;AAAC,gBAAM,SAAS,KAAI;AAAC,cAAI,MAAI,IAAI;AAAe,cAAI,KAAK,OAAM,KAAI,KAAK;AAAE,cAAI,KAAK,IAAI;AAAE,iBAAO,IAAI;AAAA,QAAY;AAAE,YAAG,uBAAsB;AAAC,uBAAW,SAAS,KAAI;AAAC,gBAAI,MAAI,IAAI;AAAe,gBAAI,KAAK,OAAM,KAAI,KAAK;AAAE,gBAAI,eAAa;AAAc,gBAAI,KAAK,IAAI;AAAE,mBAAO,IAAI,WAAW,IAAI,QAAQ;AAAA,UAAC;AAAA,QAAC;AAAC,oBAAU,SAAS,KAAI,QAAO,SAAQ;AAAC,cAAI,MAAI,IAAI;AAAe,cAAI,KAAK,OAAM,KAAI,IAAI;AAAE,cAAI,eAAa;AAAc,cAAI,SAAO,WAAU;AAAC,gBAAG,IAAI,UAAQ,OAAK,IAAI,UAAQ,KAAG,IAAI,UAAS;AAAC,qBAAO,IAAI,QAAQ;AAAE;AAAA,YAAM;AAAC,oBAAQ;AAAA,UAAC;AAAE,cAAI,UAAQ;AAAQ,cAAI,KAAK,IAAI;AAAA,QAAC;AAAA,MAAC;AAAC,uBAAe,SAAS,OAAM;AAAC,iBAAS,QAAM;AAAA,MAAK;AAAA,IAAC,OAAK;AAAA,IAAC;AAAC,QAAI,MAAIA,QAAO,OAAO,KAAG,QAAQ,IAAI,KAAK,OAAO;AAAE,QAAI,MAAIA,QAAO,UAAU,KAAG,QAAQ,KAAK,KAAK,OAAO;AAAE,SAAI,OAAO,iBAAgB;AAAC,UAAG,gBAAgB,eAAe,GAAG,GAAE;AAAC,QAAAA,QAAO,GAAG,IAAE,gBAAgB,GAAG;AAAA,MAAC;AAAA,IAAC;AAAC,sBAAgB;AAAK,QAAGA,QAAO,WAAW,EAAE,cAAWA,QAAO,WAAW;AAAE,QAAGA,QAAO,aAAa,EAAE,eAAYA,QAAO,aAAa;AAAE,QAAGA,QAAO,MAAM,EAAE,SAAMA,QAAO,MAAM;AAAE,aAAS,wBAAwB,MAAK,KAAI;AAAC,UAAG,OAAO,YAAY,aAAW,YAAW;AAAC,YAAI,YAAU,EAAC,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK;AAAE,YAAI,OAAK,EAAC,YAAW,CAAC,GAAE,SAAQ,IAAI,CAAC,KAAG,MAAI,CAAC,IAAE,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,EAAC;AAAE,iBAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,EAAE,GAAE;AAAC,eAAK,WAAW,KAAK,UAAU,IAAI,CAAC,CAAC,CAAC;AAAA,QAAC;AAAC,eAAO,IAAI,YAAY,SAAS,MAAK,IAAI;AAAA,MAAC;AAAC,UAAI,cAAY,CAAC,GAAE,GAAE,GAAE,EAAE;AAAE,UAAI,SAAO,IAAI,MAAM,GAAE,CAAC;AAAE,UAAI,WAAS,IAAI,MAAM,CAAC;AAAE,UAAI,YAAU,EAAC,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG;AAAE,kBAAY,KAAK,SAAS,MAAM;AAAE,eAAQ,IAAE,GAAE,IAAE,SAAS,QAAO,EAAE,GAAE;AAAC,oBAAY,KAAK,UAAU,SAAS,CAAC,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,UAAQ,KAAI;AAAC,oBAAY,KAAK,CAAC;AAAA,MAAC,OAAK;AAAC,sBAAY,YAAY,OAAO,CAAC,GAAE,UAAU,MAAM,CAAC,CAAC;AAAA,MAAC;AAAC,kBAAY,CAAC,IAAE,YAAY,SAAO;AAAE,UAAI,QAAM,IAAI,WAAW,CAAC,GAAE,IAAG,KAAI,KAAI,GAAE,GAAE,GAAE,CAAC,EAAE,OAAO,aAAY,CAAC,GAAE,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,KAAI,GAAE,CAAC,CAAC,CAAC;AAAE,UAAI,SAAO,IAAI,YAAY,OAAO,KAAK;AAAE,UAAI,WAAS,IAAI,YAAY,SAAS,QAAO,EAAC,KAAI,EAAC,KAAI,KAAI,EAAC,CAAC;AAAE,UAAI,cAAY,SAAS,QAAQ,GAAG;AAAE,aAAO;AAAA,IAAW;AAAC,QAAI,mBAAiB,CAAC;AAAE,QAAI;AAAoB,aAAS,oBAAmB;AAAC,UAAG,iBAAiB,QAAO;AAAC,eAAO,iBAAiB,IAAI;AAAA,MAAC;AAAC,UAAG;AAAC,kBAAU,KAAK,CAAC;AAAA,MAAC,SAAOC,MAAI;AAAC,YAAG,EAAEA,gBAAe,aAAY;AAAC,gBAAMA;AAAA,QAAG;AAAC,cAAK;AAAA,MAAoD;AAAC,aAAO,UAAU,SAAO;AAAA,IAAC;AAAC,aAAS,eAAe,QAAO,OAAM;AAAC,eAAQ,IAAE,QAAO,IAAE,SAAO,OAAM,KAAI;AAAC,YAAI,OAAK,kBAAkB,CAAC;AAAE,YAAG,MAAK;AAAC,8BAAoB,IAAI,MAAK,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,YAAY,MAAK,KAAI;AAAC,UAAG,CAAC,qBAAoB;AAAC,8BAAoB,oBAAI;AAAQ,uBAAe,GAAE,UAAU,MAAM;AAAA,MAAC;AAAC,UAAG,oBAAoB,IAAI,IAAI,GAAE;AAAC,eAAO,oBAAoB,IAAI,IAAI;AAAA,MAAC;AAAC,UAAI,MAAI,kBAAkB;AAAE,UAAG;AAAC,0BAAkB,KAAI,IAAI;AAAA,MAAC,SAAOA,MAAI;AAAC,YAAG,EAAEA,gBAAe,YAAW;AAAC,gBAAMA;AAAA,QAAG;AAAC,YAAI,UAAQ,wBAAwB,MAAK,GAAG;AAAE,0BAAkB,KAAI,OAAO;AAAA,MAAC;AAAC,0BAAoB,IAAI,MAAK,GAAG;AAAE,aAAO;AAAA,IAAG;AAAC,QAAI;AAAW,QAAGD,QAAO,YAAY,EAAE,cAAWA,QAAO,YAAY;AAAE,QAAI,gBAAcA,QAAO,eAAe,KAAG;AAAK,QAAG,OAAO,gBAAc,UAAS;AAAC,YAAM,iCAAiC;AAAA,IAAC;AAAC,QAAI;AAAW,QAAI,QAAM;AAAM,QAAI;AAAW,aAAS,OAAO,WAAU,MAAK;AAAC,UAAG,CAAC,WAAU;AAAC,cAAM,uBAAqB,IAAI;AAAA,MAAC;AAAA,IAAC;AAAC,QAAI,cAAY,OAAO,gBAAc,cAAY,IAAI,YAAY,MAAM,IAAE;AAAU,aAAS,kBAAkB,MAAK,KAAI,gBAAe;AAAC,UAAI,SAAO,MAAI;AAAe,UAAI,SAAO;AAAI,aAAM,KAAK,MAAM,KAAG,EAAE,UAAQ,QAAQ,GAAE;AAAO,UAAG,SAAO,MAAI,MAAI,KAAK,YAAU,aAAY;AAAC,eAAO,YAAY,OAAO,KAAK,SAAS,KAAI,MAAM,CAAC;AAAA,MAAC,OAAK;AAAC,YAAI,MAAI;AAAG,eAAM,MAAI,QAAO;AAAC,cAAI,KAAG,KAAK,KAAK;AAAE,cAAG,EAAE,KAAG,MAAK;AAAC,mBAAK,OAAO,aAAa,EAAE;AAAE;AAAA,UAAQ;AAAC,cAAI,KAAG,KAAK,KAAK,IAAE;AAAG,eAAI,KAAG,QAAM,KAAI;AAAC,mBAAK,OAAO,cAAc,KAAG,OAAK,IAAE,EAAE;AAAE;AAAA,UAAQ;AAAC,cAAI,KAAG,KAAK,KAAK,IAAE;AAAG,eAAI,KAAG,QAAM,KAAI;AAAC,kBAAI,KAAG,OAAK,KAAG,MAAI,IAAE;AAAA,UAAE,OAAK;AAAC,kBAAI,KAAG,MAAI,KAAG,MAAI,KAAG,MAAI,IAAE,KAAK,KAAK,IAAE;AAAA,UAAE;AAAC,cAAG,KAAG,OAAM;AAAC,mBAAK,OAAO,aAAa,EAAE;AAAA,UAAC,OAAK;AAAC,gBAAI,KAAG,KAAG;AAAM,mBAAK,OAAO,aAAa,QAAM,MAAI,IAAG,QAAM,KAAG,IAAI;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO;AAAA,IAAG;AAAC,aAAS,aAAa,KAAI,gBAAe;AAAC,aAAO,MAAI,kBAAkB,QAAO,KAAI,cAAc,IAAE;AAAA,IAAE;AAAC,aAAS,kBAAkB,KAAI,MAAK,QAAO,iBAAgB;AAAC,UAAG,EAAE,kBAAgB,GAAG,QAAO;AAAE,UAAI,WAAS;AAAO,UAAI,SAAO,SAAO,kBAAgB;AAAE,eAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,EAAE,GAAE;AAAC,YAAI,IAAE,IAAI,WAAW,CAAC;AAAE,YAAG,KAAG,SAAO,KAAG,OAAM;AAAC,cAAI,KAAG,IAAI,WAAW,EAAE,CAAC;AAAE,cAAE,UAAQ,IAAE,SAAO,MAAI,KAAG;AAAA,QAAI;AAAC,YAAG,KAAG,KAAI;AAAC,cAAG,UAAQ,OAAO;AAAM,eAAK,QAAQ,IAAE;AAAA,QAAC,WAAS,KAAG,MAAK;AAAC,cAAG,SAAO,KAAG,OAAO;AAAM,eAAK,QAAQ,IAAE,MAAI,KAAG;AAAE,eAAK,QAAQ,IAAE,MAAI,IAAE;AAAA,QAAE,WAAS,KAAG,OAAM;AAAC,cAAG,SAAO,KAAG,OAAO;AAAM,eAAK,QAAQ,IAAE,MAAI,KAAG;AAAG,eAAK,QAAQ,IAAE,MAAI,KAAG,IAAE;AAAG,eAAK,QAAQ,IAAE,MAAI,IAAE;AAAA,QAAE,OAAK;AAAC,cAAG,SAAO,KAAG,OAAO;AAAM,eAAK,QAAQ,IAAE,MAAI,KAAG;AAAG,eAAK,QAAQ,IAAE,MAAI,KAAG,KAAG;AAAG,eAAK,QAAQ,IAAE,MAAI,KAAG,IAAE;AAAG,eAAK,QAAQ,IAAE,MAAI,IAAE;AAAA,QAAE;AAAA,MAAC;AAAC,WAAK,MAAM,IAAE;AAAE,aAAO,SAAO;AAAA,IAAQ;AAAC,aAAS,gBAAgB,KAAI;AAAC,UAAI,MAAI;AAAE,eAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,EAAE,GAAE;AAAC,YAAI,IAAE,IAAI,WAAW,CAAC;AAAE,YAAG,KAAG,SAAO,KAAG,MAAM,KAAE,UAAQ,IAAE,SAAO,MAAI,IAAI,WAAW,EAAE,CAAC,IAAE;AAAK,YAAG,KAAG,IAAI,GAAE;AAAA,iBAAY,KAAG,KAAK,QAAK;AAAA,iBAAU,KAAG,MAAM,QAAK;AAAA,YAAO,QAAK;AAAA,MAAC;AAAC,aAAO;AAAA,IAAG;AAAC,aAAS,mBAAmB,OAAME,SAAO;AAAC,YAAM,IAAI,OAAMA,OAAM;AAAA,IAAC;AAAC,aAAS,mBAAmB,KAAIA,SAAO,aAAY;AAAC,eAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,EAAE,GAAE;AAAC,cAAMA,aAAU,CAAC,IAAE,IAAI,WAAW,CAAC;AAAA,MAAC;AAAC,UAAG,CAAC,YAAY,OAAMA,WAAQ,CAAC,IAAE;AAAA,IAAC;AAAC,aAAS,QAAQ,GAAE,UAAS;AAAC,UAAG,IAAE,WAAS,GAAE;AAAC,aAAG,WAAS,IAAE;AAAA,MAAQ;AAAC,aAAO;AAAA,IAAC;AAAC,QAAI,QAAO,OAAM,QAAO,QAAO,SAAQ,QAAO,SAAQ,SAAQ;AAAQ,aAAS,2BAA2B,KAAI;AAAC,eAAO;AAAI,MAAAF,QAAO,OAAO,IAAE,QAAM,IAAI,UAAU,GAAG;AAAE,MAAAA,QAAO,QAAQ,IAAE,SAAO,IAAI,WAAW,GAAG;AAAE,MAAAA,QAAO,QAAQ,IAAE,SAAO,IAAI,WAAW,GAAG;AAAE,MAAAA,QAAO,QAAQ,IAAE,SAAO,IAAI,WAAW,GAAG;AAAE,MAAAA,QAAO,SAAS,IAAE,UAAQ,IAAI,YAAY,GAAG;AAAE,MAAAA,QAAO,SAAS,IAAE,UAAQ,IAAI,YAAY,GAAG;AAAE,MAAAA,QAAO,SAAS,IAAE,UAAQ,IAAI,aAAa,GAAG;AAAE,MAAAA,QAAO,SAAS,IAAE,UAAQ,IAAI,aAAa,GAAG;AAAA,IAAC;AAAC,QAAI,iBAAeA,QAAO,gBAAgB,KAAG;AAAS,QAAI;AAAU,QAAI,eAAa,CAAC;AAAE,QAAI,aAAW,CAAC;AAAE,QAAI,gBAAc,CAAC;AAAE,QAAI,qBAAmB;AAAM,aAAS,SAAQ;AAAC,UAAGA,QAAO,QAAQ,GAAE;AAAC,YAAG,OAAOA,QAAO,QAAQ,KAAG,WAAW,CAAAA,QAAO,QAAQ,IAAE,CAACA,QAAO,QAAQ,CAAC;AAAE,eAAMA,QAAO,QAAQ,EAAE,QAAO;AAAC,sBAAYA,QAAO,QAAQ,EAAE,MAAM,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,2BAAqB,YAAY;AAAA,IAAC;AAAC,aAAS,cAAa;AAAC,2BAAmB;AAAK,2BAAqB,UAAU;AAAA,IAAC;AAAC,aAAS,UAAS;AAAC,UAAGA,QAAO,SAAS,GAAE;AAAC,YAAG,OAAOA,QAAO,SAAS,KAAG,WAAW,CAAAA,QAAO,SAAS,IAAE,CAACA,QAAO,SAAS,CAAC;AAAE,eAAMA,QAAO,SAAS,EAAE,QAAO;AAAC,uBAAaA,QAAO,SAAS,EAAE,MAAM,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,2BAAqB,aAAa;AAAA,IAAC;AAAC,aAAS,YAAY,IAAG;AAAC,mBAAa,QAAQ,EAAE;AAAA,IAAC;AAAC,aAAS,UAAU,IAAG;AAAC,iBAAW,QAAQ,EAAE;AAAA,IAAC;AAAC,aAAS,aAAa,IAAG;AAAC,oBAAc,QAAQ,EAAE;AAAA,IAAC;AAAC,QAAI,kBAAgB;AAAE,QAAI,uBAAqB;AAAK,QAAI,wBAAsB;AAAK,aAAS,iBAAiB,IAAG;AAAC;AAAkB,UAAGA,QAAO,wBAAwB,GAAE;AAAC,QAAAA,QAAO,wBAAwB,EAAE,eAAe;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,oBAAoB,IAAG;AAAC;AAAkB,UAAGA,QAAO,wBAAwB,GAAE;AAAC,QAAAA,QAAO,wBAAwB,EAAE,eAAe;AAAA,MAAC;AAAC,UAAG,mBAAiB,GAAE;AAAC,YAAG,yBAAuB,MAAK;AAAC,wBAAc,oBAAoB;AAAE,iCAAqB;AAAA,QAAI;AAAC,YAAG,uBAAsB;AAAC,cAAI,WAAS;AAAsB,kCAAsB;AAAK,mBAAS;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAA,QAAO,iBAAiB,IAAE,CAAC;AAAE,IAAAA,QAAO,iBAAiB,IAAE,CAAC;AAAE,aAAS,MAAM,MAAK;AAAC;AAAC,YAAGA,QAAO,SAAS,GAAE;AAAC,UAAAA,QAAO,SAAS,EAAE,IAAI;AAAA,QAAC;AAAA,MAAC;AAAC,aAAK,aAAW,OAAK;AAAI,UAAI,IAAI;AAAE,cAAM;AAAK,mBAAW;AAAE,cAAM;AAA8C,UAAI,IAAE,IAAI,YAAY,aAAa,IAAI;AAAE,yBAAmB,CAAC;AAAE,YAAM;AAAA,IAAC;AAAC,QAAI,gBAAc;AAAwC,aAAS,UAAU,UAAS;AAAC,aAAO,SAAS,WAAW,aAAa;AAAA,IAAC;AAAC,QAAI;AAAe,qBAAe;AAAe,QAAG,CAAC,UAAU,cAAc,GAAE;AAAC,uBAAe,WAAW,cAAc;AAAA,IAAC;AAAC,aAAS,UAAU,MAAK;AAAC,UAAG;AAAC,YAAG,QAAM,kBAAgB,YAAW;AAAC,iBAAO,IAAI,WAAW,UAAU;AAAA,QAAC;AAAC,YAAG,YAAW;AAAC,iBAAO,WAAW,IAAI;AAAA,QAAC,OAAK;AAAC,gBAAK;AAAA,QAAiD;AAAA,MAAC,SAAOC,MAAI;AAAC,cAAMA,IAAG;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,mBAAkB;AAAC,UAAG,CAAC,eAAa,sBAAoB,wBAAuB;AAAC,YAAG,OAAO,UAAQ,YAAW;AAAC,iBAAO,MAAM,gBAAe,EAAC,aAAY,cAAa,CAAC,EAAE,KAAK,SAAS,UAAS;AAAC,gBAAG,CAAC,SAAS,IAAI,GAAE;AAAC,oBAAK,yCAAuC,iBAAe;AAAA,YAAG;AAAC,mBAAO,SAAS,aAAa,EAAE;AAAA,UAAC,CAAC,EAAE,MAAM,WAAU;AAAC,mBAAO,UAAU,cAAc;AAAA,UAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO,QAAQ,QAAQ,EAAE,KAAK,WAAU;AAAC,eAAO,UAAU,cAAc;AAAA,MAAC,CAAC;AAAA,IAAC;AAAC,aAAS,aAAY;AAAC,UAAI,OAAK,EAAC,KAAI,cAAa;AAAE,eAAS,gBAAgB,UAAS,QAAO;AAAC,YAAIE,WAAQ,SAAS;AAAQ,QAAAH,QAAO,KAAK,IAAEG;AAAQ,qBAAWH,QAAO,KAAK,EAAE,GAAG;AAAE,mCAA2B,WAAW,MAAM;AAAE,oBAAUA,QAAO,KAAK,EAAE,IAAI;AAAE,kBAAUA,QAAO,KAAK,EAAE,GAAG,CAAC;AAAE,4BAAoB,kBAAkB;AAAA,MAAC;AAAC,uBAAiB,kBAAkB;AAAE,eAAS,2BAA2B,QAAO;AAAC,wBAAgB,OAAO,UAAU,CAAC;AAAA,MAAC;AAAC,eAAS,uBAAuB,UAAS;AAAC,eAAO,iBAAiB,EAAE,KAAK,SAAS,QAAO;AAAC,iBAAO,YAAY,YAAY,QAAO,IAAI;AAAA,QAAC,CAAC,EAAE,KAAK,SAAS,UAAS;AAAC,iBAAO;AAAA,QAAQ,CAAC,EAAE,KAAK,UAAS,SAAS,QAAO;AAAC,cAAI,4CAA0C,MAAM;AAAE,gBAAM,MAAM;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC,eAAS,mBAAkB;AAAC,YAAG,CAAC,cAAY,OAAO,YAAY,yBAAuB,cAAY,CAAC,UAAU,cAAc,KAAG,OAAO,UAAQ,YAAW;AAAC,iBAAO,MAAM,gBAAe,EAAC,aAAY,cAAa,CAAC,EAAE,KAAK,SAAS,UAAS;AAAC,gBAAI,SAAO,YAAY,qBAAqB,UAAS,IAAI;AAAE,mBAAO,OAAO,KAAK,4BAA2B,SAAS,QAAO;AAAC,kBAAI,oCAAkC,MAAM;AAAE,kBAAI,2CAA2C;AAAE,qBAAO,uBAAuB,0BAA0B;AAAA,YAAC,CAAC;AAAA,UAAC,CAAC;AAAA,QAAC,OAAK;AAAC,iBAAO,uBAAuB,0BAA0B;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGA,QAAO,iBAAiB,GAAE;AAAC,YAAG;AAAC,cAAI,UAAQA,QAAO,iBAAiB,EAAE,MAAK,eAAe;AAAE,iBAAO;AAAA,QAAO,SAAO,GAAE;AAAC,cAAI,wDAAsD,CAAC;AAAE,iBAAO;AAAA,QAAK;AAAA,MAAC;AAAC,uBAAiB,EAAE,MAAM,kBAAkB;AAAE,aAAM,CAAC;AAAA,IAAC;AAAC,aAAS,qBAAqB,WAAU;AAAC,aAAM,UAAU,SAAO,GAAE;AAAC,YAAI,WAAS,UAAU,MAAM;AAAE,YAAG,OAAO,YAAU,YAAW;AAAC,mBAASA,OAAM;AAAE;AAAA,QAAQ;AAAC,YAAI,OAAK,SAAS;AAAK,YAAG,OAAO,SAAO,UAAS;AAAC,cAAG,SAAS,QAAM,QAAU;AAAC,8BAAkB,IAAI,EAAE;AAAA,UAAC,OAAK;AAAC,8BAAkB,IAAI,EAAE,SAAS,GAAG;AAAA,UAAC;AAAA,QAAC,OAAK;AAAC,eAAK,SAAS,QAAM,SAAU,OAAK,SAAS,GAAG;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAI,kBAAgB,CAAC;AAAE,aAAS,kBAAkB,SAAQ;AAAC,UAAI,OAAK,gBAAgB,OAAO;AAAE,UAAG,CAAC,MAAK;AAAC,YAAG,WAAS,gBAAgB,OAAO,iBAAgB,SAAO,UAAQ;AAAE,wBAAgB,OAAO,IAAE,OAAK,UAAU,IAAI,OAAO;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI;AAAC,aAAS,kBAAkB,KAAI,MAAK;AAAC,gBAAU,IAAI,KAAI,IAAI;AAAE,sBAAgB,GAAG,IAAE;AAAA,IAAI;AAAC,aAAS,0BAA0B,MAAK;AAAC,aAAO,QAAQ,OAAK,EAAE,IAAE;AAAA,IAAE;AAAC,aAAS,cAAc,QAAO;AAAC,WAAK,SAAO;AAAO,WAAK,MAAI,SAAO;AAAG,WAAK,WAAS,SAAS,MAAK;AAAC,eAAO,KAAK,MAAI,KAAG,CAAC,IAAE;AAAA,MAAI;AAAE,WAAK,WAAS,WAAU;AAAC,eAAO,OAAO,KAAK,MAAI,KAAG,CAAC;AAAA,MAAC;AAAE,WAAK,iBAAe,SAAS,YAAW;AAAC,eAAO,KAAK,MAAI,KAAG,CAAC,IAAE;AAAA,MAAU;AAAE,WAAK,iBAAe,WAAU;AAAC,eAAO,OAAO,KAAK,MAAI,KAAG,CAAC;AAAA,MAAC;AAAE,WAAK,eAAa,SAAS,UAAS;AAAC,eAAO,KAAK,OAAK,CAAC,IAAE;AAAA,MAAQ;AAAE,WAAK,aAAW,SAAS,QAAO;AAAC,iBAAO,SAAO,IAAE;AAAE,cAAM,KAAK,MAAI,MAAI,CAAC,IAAE;AAAA,MAAM;AAAE,WAAK,aAAW,WAAU;AAAC,eAAO,MAAM,KAAK,MAAI,MAAI,CAAC,KAAG;AAAA,MAAC;AAAE,WAAK,eAAa,SAAS,UAAS;AAAC,mBAAS,WAAS,IAAE;AAAE,cAAM,KAAK,MAAI,MAAI,CAAC,IAAE;AAAA,MAAQ;AAAE,WAAK,eAAa,WAAU;AAAC,eAAO,MAAM,KAAK,MAAI,MAAI,CAAC,KAAG;AAAA,MAAC;AAAE,WAAK,OAAK,SAAS,MAAK,YAAW;AAAC,aAAK,SAAS,IAAI;AAAE,aAAK,eAAe,UAAU;AAAE,aAAK,aAAa,CAAC;AAAE,aAAK,WAAW,KAAK;AAAE,aAAK,aAAa,KAAK;AAAA,MAAC;AAAE,WAAK,UAAQ,WAAU;AAAC,YAAI,QAAM,OAAO,KAAK,OAAK,CAAC;AAAE,eAAO,KAAK,OAAK,CAAC,IAAE,QAAM;AAAA,MAAC;AAAE,WAAK,cAAY,WAAU;AAAC,YAAI,OAAK,OAAO,KAAK,OAAK,CAAC;AAAE,eAAO,KAAK,OAAK,CAAC,IAAE,OAAK;AAAE,eAAO,SAAO;AAAA,MAAC;AAAA,IAAC;AAAC,QAAI,gBAAc;AAAE,QAAI,yBAAuB;AAAE,aAAS,aAAa,KAAI,MAAK,YAAW;AAAC,UAAI,OAAK,IAAI,cAAc,GAAG;AAAE,WAAK,KAAK,MAAK,UAAU;AAAE,sBAAc;AAAI;AAAyB,YAAM;AAAA,IAAG;AAAC,aAAS,SAAQ;AAAC,YAAM,EAAE;AAAA,IAAC;AAAC,aAAS,uBAAuB,MAAK,KAAI,KAAI;AAAC,aAAO,WAAW,MAAK,KAAI,MAAI,GAAG;AAAA,IAAC;AAAC,aAAS,0BAA0B,MAAK;AAAC,UAAG;AAAC,mBAAW,KAAK,OAAK,OAAO,aAAW,UAAQ,EAAE;AAAE,mCAA2B,WAAW,MAAM;AAAE,eAAO;AAAA,MAAC,SAAO,GAAE;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,wBAAwB,eAAc;AAAC,UAAI,UAAQ,OAAO;AAAO,sBAAc,kBAAgB;AAAE,UAAI,cAAY;AAAW,UAAG,gBAAc,aAAY;AAAC,eAAO;AAAA,MAAK;AAAC,eAAQ,UAAQ,GAAE,WAAS,GAAE,WAAS,GAAE;AAAC,YAAI,oBAAkB,WAAS,IAAE,MAAG;AAAS,4BAAkB,KAAK,IAAI,mBAAkB,gBAAc,SAAS;AAAE,YAAI,UAAQ,KAAK,IAAI,aAAY,QAAQ,KAAK,IAAI,eAAc,iBAAiB,GAAE,KAAK,CAAC;AAAE,YAAI,cAAY,0BAA0B,OAAO;AAAE,YAAG,aAAY;AAAC,iBAAO;AAAA,QAAI;AAAA,MAAC;AAAC,aAAO;AAAA,IAAK;AAAC,QAAI,MAAI,CAAC;AAAE,aAAS,oBAAmB;AAAC,aAAO,eAAa;AAAA,IAAgB;AAAC,aAAS,gBAAe;AAAC,UAAG,CAAC,cAAc,SAAQ;AAAC,YAAI,QAAM,OAAO,cAAY,YAAU,UAAU,aAAW,UAAU,UAAU,CAAC,KAAG,KAAK,QAAQ,KAAI,GAAG,IAAE;AAAS,YAAI,MAAI,EAAC,QAAO,YAAW,WAAU,YAAW,QAAO,KAAI,OAAM,KAAI,QAAO,kBAAiB,QAAO,MAAK,KAAI,kBAAkB,EAAC;AAAE,iBAAQ,KAAK,KAAI;AAAC,cAAG,IAAI,CAAC,MAAI,OAAU,QAAO,IAAI,CAAC;AAAA,cAAO,KAAI,CAAC,IAAE,IAAI,CAAC;AAAA,QAAC;AAAC,YAAI,UAAQ,CAAC;AAAE,iBAAQ,KAAK,KAAI;AAAC,kBAAQ,KAAK,IAAE,MAAI,IAAI,CAAC,CAAC;AAAA,QAAC;AAAC,sBAAc,UAAQ;AAAA,MAAO;AAAC,aAAO,cAAc;AAAA,IAAO;AAAC,QAAI,WAAS,EAAC,UAAS,CAAC,GAAE,SAAQ,CAAC,MAAK,CAAC,GAAE,CAAC,CAAC,GAAE,WAAU,SAAS,QAAO,MAAK;AAAC,UAAIE,UAAO,SAAS,QAAQ,MAAM;AAAE,UAAG,SAAO,KAAG,SAAO,IAAG;AAAC,SAAC,WAAS,IAAE,MAAI,KAAK,kBAAkBA,SAAO,CAAC,CAAC;AAAE,QAAAA,QAAO,SAAO;AAAA,MAAC,OAAK;AAAC,QAAAA,QAAO,KAAK,IAAI;AAAA,MAAC;AAAA,IAAC,GAAE,SAAQ,QAAU,KAAI,WAAU;AAAC,eAAS,WAAS;AAAE,UAAI,MAAI,OAAO,SAAS,UAAQ,KAAG,CAAC;AAAE,aAAO;AAAA,IAAG,GAAE,QAAO,SAAS,KAAI;AAAC,UAAI,MAAI,aAAa,GAAG;AAAE,aAAO;AAAA,IAAG,GAAE,OAAM,SAAS,KAAI,MAAK;AAAC,aAAO;AAAA,IAAG,EAAC;AAAE,aAAS,aAAa,WAAU,aAAY;AAAC,UAAI,UAAQ;AAAE,oBAAc,EAAE,QAAQ,SAAS,QAAO,GAAE;AAAC,YAAI,MAAI,cAAY;AAAQ,eAAO,YAAU,IAAE,KAAG,CAAC,IAAE;AAAI,2BAAmB,QAAO,GAAG;AAAE,mBAAS,OAAO,SAAO;AAAA,MAAC,CAAC;AAAE,aAAO;AAAA,IAAC;AAAC,aAAS,mBAAmB,gBAAe,mBAAkB;AAAC,UAAI,UAAQ,cAAc;AAAE,aAAO,kBAAgB,CAAC,IAAE,QAAQ;AAAO,UAAI,UAAQ;AAAE,cAAQ,QAAQ,SAAS,QAAO;AAAC,mBAAS,OAAO,SAAO;AAAA,MAAC,CAAC;AAAE,aAAO,qBAAmB,CAAC,IAAE;AAAQ,aAAO;AAAA,IAAC;AAAC,aAAS,UAAU,IAAG;AAAC,aAAO;AAAA,IAAC;AAAC,aAAS,SAAS,IAAG,KAAI,QAAO,MAAK;AAAC,UAAI,SAAO,SAAS,gBAAgB,EAAE;AAAE,UAAI,MAAI,SAAS,QAAQ,QAAO,KAAI,MAAM;AAAE,aAAO,QAAM,CAAC,IAAE;AAAI,aAAO;AAAA,IAAC;AAAC,aAAS,SAAS,IAAG,YAAW,aAAY,QAAO,WAAU;AAAA,IAAC;AAAC,aAAS,UAAU,IAAG,KAAI,QAAO,MAAK;AAAC,UAAI,MAAI;AAAE,eAAQ,IAAE,GAAE,IAAE,QAAO,KAAI;AAAC,YAAI,MAAI,OAAO,OAAK,CAAC;AAAE,YAAI,MAAI,OAAO,MAAI,KAAG,CAAC;AAAE,eAAK;AAAE,iBAAQ,IAAE,GAAE,IAAE,KAAI,KAAI;AAAC,mBAAS,UAAU,IAAG,OAAO,MAAI,CAAC,CAAC;AAAA,QAAC;AAAC,eAAK;AAAA,MAAG;AAAC,aAAO,QAAM,CAAC,IAAE;AAAI,aAAO;AAAA,IAAC;AAAC,aAAS,aAAa,MAAK;AAAC,aAAO,OAAK,MAAI,MAAI,OAAK,QAAM,KAAG,OAAK,QAAM;AAAA,IAAE;AAAC,aAAS,WAAW,OAAM,OAAM;AAAC,UAAI,MAAI;AAAE,eAAQ,IAAE,GAAE,KAAG,OAAM,OAAK,MAAM,GAAG,GAAE;AAAA,MAAC;AAAC,aAAO;AAAA,IAAG;AAAC,QAAI,oBAAkB,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,QAAI,uBAAqB,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,aAAS,UAAU,MAAK,MAAK;AAAC,UAAI,UAAQ,IAAI,KAAK,KAAK,QAAQ,CAAC;AAAE,aAAM,OAAK,GAAE;AAAC,YAAI,OAAK,aAAa,QAAQ,YAAY,CAAC;AAAE,YAAI,eAAa,QAAQ,SAAS;AAAE,YAAI,sBAAoB,OAAK,oBAAkB,sBAAsB,YAAY;AAAE,YAAG,OAAK,qBAAmB,QAAQ,QAAQ,GAAE;AAAC,kBAAM,qBAAmB,QAAQ,QAAQ,IAAE;AAAE,kBAAQ,QAAQ,CAAC;AAAE,cAAG,eAAa,IAAG;AAAC,oBAAQ,SAAS,eAAa,CAAC;AAAA,UAAC,OAAK;AAAC,oBAAQ,SAAS,CAAC;AAAE,oBAAQ,YAAY,QAAQ,YAAY,IAAE,CAAC;AAAA,UAAC;AAAA,QAAC,OAAK;AAAC,kBAAQ,QAAQ,QAAQ,QAAQ,IAAE,IAAI;AAAE,iBAAO;AAAA,QAAO;AAAA,MAAC;AAAC,aAAO;AAAA,IAAO;AAAC,aAAS,UAAU,GAAE,SAAQ,QAAO,IAAG;AAAC,UAAI,UAAQ,OAAO,KAAG,MAAI,CAAC;AAAE,UAAI,OAAK,EAAC,QAAO,OAAO,MAAI,CAAC,GAAE,QAAO,OAAO,KAAG,KAAG,CAAC,GAAE,SAAQ,OAAO,KAAG,KAAG,CAAC,GAAE,SAAQ,OAAO,KAAG,MAAI,CAAC,GAAE,QAAO,OAAO,KAAG,MAAI,CAAC,GAAE,SAAQ,OAAO,KAAG,MAAI,CAAC,GAAE,SAAQ,OAAO,KAAG,MAAI,CAAC,GAAE,SAAQ,OAAO,KAAG,MAAI,CAAC,GAAE,UAAS,OAAO,KAAG,MAAI,CAAC,GAAE,WAAU,OAAO,KAAG,MAAI,CAAC,GAAE,SAAQ,UAAQ,aAAa,OAAO,IAAE,GAAE;AAAE,UAAI,UAAQ,aAAa,MAAM;AAAE,UAAI,oBAAkB,EAAC,MAAK,wBAAuB,MAAK,YAAW,MAAK,YAAW,MAAK,MAAK,MAAK,eAAc,MAAK,SAAQ,MAAK,YAAW,MAAK,YAAW,MAAK,YAAW,OAAM,MAAK,OAAM,MAAK,OAAM,YAAW,OAAM,YAAW,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,MAAK,OAAM,KAAI;AAAE,eAAQ,QAAQ,mBAAkB;AAAC,kBAAQ,QAAQ,QAAQ,IAAI,OAAO,MAAK,GAAG,GAAE,kBAAkB,IAAI,CAAC;AAAA,MAAC;AAAC,UAAI,WAAS,CAAC,UAAS,UAAS,WAAU,aAAY,YAAW,UAAS,UAAU;AAAE,UAAI,SAAO,CAAC,WAAU,YAAW,SAAQ,SAAQ,OAAM,QAAO,QAAO,UAAS,aAAY,WAAU,YAAW,UAAU;AAAE,eAAS,iBAAiB,OAAM,QAAO,WAAU;AAAC,YAAI,MAAI,OAAO,UAAQ,WAAS,MAAM,SAAS,IAAE,SAAO;AAAG,eAAM,IAAI,SAAO,QAAO;AAAC,gBAAI,UAAU,CAAC,IAAE;AAAA,QAAG;AAAC,eAAO;AAAA,MAAG;AAAC,eAAS,aAAa,OAAM,QAAO;AAAC,eAAO,iBAAiB,OAAM,QAAO,GAAG;AAAA,MAAC;AAAC,eAAS,aAAa,OAAM,OAAM;AAAC,iBAAS,IAAI,OAAM;AAAC,iBAAO,QAAM,IAAE,KAAG,QAAM,IAAE,IAAE;AAAA,QAAC;AAAC,YAAIE;AAAQ,aAAIA,WAAQ,IAAI,MAAM,YAAY,IAAE,MAAM,YAAY,CAAC,OAAK,GAAE;AAAC,eAAIA,WAAQ,IAAI,MAAM,SAAS,IAAE,MAAM,SAAS,CAAC,OAAK,GAAE;AAAC,YAAAA,WAAQ,IAAI,MAAM,QAAQ,IAAE,MAAM,QAAQ,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,eAAOA;AAAA,MAAO;AAAC,eAAS,sBAAsB,WAAU;AAAC,gBAAO,UAAU,OAAO,GAAE;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAI,KAAK,UAAU,YAAY,IAAE,GAAE,IAAG,EAAE;AAAA,UAAE,KAAK;AAAE,mBAAO;AAAA,UAAU,KAAK;AAAE,mBAAO,IAAI,KAAK,UAAU,YAAY,GAAE,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAI,KAAK,UAAU,YAAY,GAAE,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAI,KAAK,UAAU,YAAY,GAAE,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAI,KAAK,UAAU,YAAY,IAAE,GAAE,IAAG,EAAE;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAI,KAAK,UAAU,YAAY,IAAE,GAAE,IAAG,EAAE;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,iBAAiBC,OAAK;AAAC,YAAI,WAAS,UAAU,IAAI,KAAKA,MAAK,UAAQ,MAAK,GAAE,CAAC,GAAEA,MAAK,OAAO;AAAE,YAAI,oBAAkB,IAAI,KAAK,SAAS,YAAY,GAAE,GAAE,CAAC;AAAE,YAAI,oBAAkB,IAAI,KAAK,SAAS,YAAY,IAAE,GAAE,GAAE,CAAC;AAAE,YAAI,yBAAuB,sBAAsB,iBAAiB;AAAE,YAAI,yBAAuB,sBAAsB,iBAAiB;AAAE,YAAG,aAAa,wBAAuB,QAAQ,KAAG,GAAE;AAAC,cAAG,aAAa,wBAAuB,QAAQ,KAAG,GAAE;AAAC,mBAAO,SAAS,YAAY,IAAE;AAAA,UAAC,OAAK;AAAC,mBAAO,SAAS,YAAY;AAAA,UAAC;AAAA,QAAC,OAAK;AAAC,iBAAO,SAAS,YAAY,IAAE;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,oBAAkB,EAAC,MAAK,SAASA,OAAK;AAAC,eAAO,SAASA,MAAK,OAAO,EAAE,UAAU,GAAE,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,SAASA,MAAK,OAAO;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,OAAOA,MAAK,MAAM,EAAE,UAAU,GAAE,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,OAAOA,MAAK,MAAM;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,YAAI,OAAKA,MAAK,UAAQ;AAAK,eAAO,aAAa,OAAK,MAAI,GAAE,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,aAAaA,MAAK,SAAQ,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,iBAAiBA,MAAK,SAAQ,GAAE,GAAG;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,iBAAiBA,KAAI,EAAE,SAAS,EAAE,UAAU,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,iBAAiBA,KAAI;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,aAAaA,MAAK,SAAQ,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,YAAI,aAAWA,MAAK;AAAQ,YAAG,cAAY,EAAE,cAAW;AAAA,iBAAW,aAAW,GAAG,eAAY;AAAG,eAAO,aAAa,YAAW,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,aAAaA,MAAK,UAAQ,WAAW,aAAaA,MAAK,UAAQ,IAAI,IAAE,oBAAkB,sBAAqBA,MAAK,SAAO,CAAC,GAAE,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,aAAaA,MAAK,SAAO,GAAE,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,aAAaA,MAAK,QAAO,CAAC;AAAA,MAAC,GAAE,MAAK,WAAU;AAAC,eAAM;AAAA,MAAI,GAAE,MAAK,SAASA,OAAK;AAAC,YAAGA,MAAK,WAAS,KAAGA,MAAK,UAAQ,IAAG;AAAC,iBAAM;AAAA,QAAI,OAAK;AAAC,iBAAM;AAAA,QAAI;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAO,aAAaA,MAAK,QAAO,CAAC;AAAA,MAAC,GAAE,MAAK,WAAU;AAAC,eAAM;AAAA,MAAI,GAAE,MAAK,SAASA,OAAK;AAAC,eAAOA,MAAK,WAAS;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,YAAI,WAAS,IAAI,KAAKA,MAAK,UAAQ,MAAK,GAAE,CAAC;AAAE,YAAI,cAAY,SAAS,OAAO,MAAI,IAAE,WAAS,UAAU,UAAS,IAAE,SAAS,OAAO,CAAC;AAAE,YAAI,UAAQ,IAAI,KAAKA,MAAK,UAAQ,MAAKA,MAAK,QAAOA,MAAK,OAAO;AAAE,YAAG,aAAa,aAAY,OAAO,IAAE,GAAE;AAAC,cAAI,6BAA2B,WAAW,aAAa,QAAQ,YAAY,CAAC,IAAE,oBAAkB,sBAAqB,QAAQ,SAAS,IAAE,CAAC,IAAE;AAAG,cAAI,6BAA2B,KAAG,YAAY,QAAQ;AAAE,cAAI,OAAK,6BAA2B,6BAA2B,QAAQ,QAAQ;AAAE,iBAAO,aAAa,KAAK,KAAK,OAAK,CAAC,GAAE,CAAC;AAAA,QAAC;AAAC,eAAO,aAAa,aAAY,QAAQ,MAAI,IAAE,OAAK;AAAA,MAAI,GAAE,MAAK,SAASA,OAAK;AAAC,YAAI,oBAAkB,IAAI,KAAKA,MAAK,UAAQ,MAAK,GAAE,CAAC;AAAE,YAAI,oBAAkB,IAAI,KAAKA,MAAK,UAAQ,MAAK,GAAE,CAAC;AAAE,YAAI,yBAAuB,sBAAsB,iBAAiB;AAAE,YAAI,yBAAuB,sBAAsB,iBAAiB;AAAE,YAAI,UAAQ,UAAU,IAAI,KAAKA,MAAK,UAAQ,MAAK,GAAE,CAAC,GAAEA,MAAK,OAAO;AAAE,YAAG,aAAa,SAAQ,sBAAsB,IAAE,GAAE;AAAC,iBAAM;AAAA,QAAI;AAAC,YAAG,aAAa,wBAAuB,OAAO,KAAG,GAAE;AAAC,iBAAM;AAAA,QAAI;AAAC,YAAI;AAAe,YAAG,uBAAuB,YAAY,IAAEA,MAAK,UAAQ,MAAK;AAAC,2BAAeA,MAAK,UAAQ,KAAG,uBAAuB,QAAQ;AAAA,QAAC,OAAK;AAAC,2BAAeA,MAAK,UAAQ,IAAE,uBAAuB,QAAQ;AAAA,QAAC;AAAC,eAAO,aAAa,KAAK,KAAK,iBAAe,CAAC,GAAE,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAOA,MAAK;AAAA,MAAO,GAAE,MAAK,SAASA,OAAK;AAAC,YAAI,WAAS,IAAI,KAAKA,MAAK,SAAQ,GAAE,CAAC;AAAE,YAAI,cAAY,SAAS,OAAO,MAAI,IAAE,WAAS,UAAU,UAAS,SAAS,OAAO,MAAI,IAAE,IAAE,IAAE,SAAS,OAAO,IAAE,CAAC;AAAE,YAAI,UAAQ,IAAI,KAAKA,MAAK,UAAQ,MAAKA,MAAK,QAAOA,MAAK,OAAO;AAAE,YAAG,aAAa,aAAY,OAAO,IAAE,GAAE;AAAC,cAAI,6BAA2B,WAAW,aAAa,QAAQ,YAAY,CAAC,IAAE,oBAAkB,sBAAqB,QAAQ,SAAS,IAAE,CAAC,IAAE;AAAG,cAAI,6BAA2B,KAAG,YAAY,QAAQ;AAAE,cAAI,OAAK,6BAA2B,6BAA2B,QAAQ,QAAQ;AAAE,iBAAO,aAAa,KAAK,KAAK,OAAK,CAAC,GAAE,CAAC;AAAA,QAAC;AAAC,eAAO,aAAa,aAAY,QAAQ,MAAI,IAAE,OAAK;AAAA,MAAI,GAAE,MAAK,SAASA,OAAK;AAAC,gBAAOA,MAAK,UAAQ,MAAM,SAAS,EAAE,UAAU,CAAC;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAOA,MAAK,UAAQ;AAAA,MAAI,GAAE,MAAK,SAASA,OAAK;AAAC,YAAI,MAAIA,MAAK;AAAU,YAAI,QAAM,OAAK;AAAE,cAAI,KAAK,IAAI,GAAG,IAAE;AAAG,cAAI,MAAI,KAAG,MAAI,MAAI;AAAG,gBAAO,QAAM,MAAI,OAAK,OAAO,SAAO,GAAG,EAAE,MAAM,EAAE;AAAA,MAAC,GAAE,MAAK,SAASA,OAAK;AAAC,eAAOA,MAAK;AAAA,MAAO,GAAE,MAAK,WAAU;AAAC,eAAM;AAAA,MAAG,EAAC;AAAE,eAAQ,QAAQ,mBAAkB;AAAC,YAAG,QAAQ,SAAS,IAAI,GAAE;AAAC,oBAAQ,QAAQ,QAAQ,IAAI,OAAO,MAAK,GAAG,GAAE,kBAAkB,IAAI,EAAE,IAAI,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,QAAM,mBAAmB,SAAQ,KAAK;AAAE,UAAG,MAAM,SAAO,SAAQ;AAAC,eAAO;AAAA,MAAC;AAAC,yBAAmB,OAAM,CAAC;AAAE,aAAO,MAAM,SAAO;AAAA,IAAC;AAAC,aAAS,YAAY,GAAE,SAAQ,QAAO,IAAG;AAAC,aAAO,UAAU,GAAE,SAAQ,QAAO,EAAE;AAAA,IAAC;AAAC,aAAS,mBAAmB,SAAQ,aAAY,QAAO;AAAC,UAAI,MAAI,SAAO,IAAE,SAAO,gBAAgB,OAAO,IAAE;AAAE,UAAI,UAAQ,IAAI,MAAM,GAAG;AAAE,UAAI,kBAAgB,kBAAkB,SAAQ,SAAQ,GAAE,QAAQ,MAAM;AAAE,UAAG,YAAY,SAAQ,SAAO;AAAgB,aAAO;AAAA,IAAO;AAAC,QAAI,gBAAc,EAAC,KAAI,2BAA0B,KAAI,cAAa,KAAI,QAAO,KAAI,wBAAuB,KAAI,yBAAwB,KAAI,cAAa,KAAI,oBAAmB,KAAI,WAAU,KAAI,UAAS,KAAI,UAAS,KAAI,WAAU,KAAI,YAAW;AAAE,QAAI,MAAI,WAAW;AAAE,QAAI,qBAAmBL,QAAO,oBAAoB,IAAE,WAAU;AAAC,cAAO,qBAAmBA,QAAO,oBAAoB,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,wCAAsCA,QAAO,uCAAuC,IAAE,WAAU;AAAC,cAAO,wCAAsCA,QAAO,uCAAuC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,wCAAsCA,QAAO,uCAAuC,IAAE,WAAU;AAAC,cAAO,wCAAsCA,QAAO,uCAAuC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,2CAAyCA,QAAO,0CAA0C,IAAE,WAAU;AAAC,cAAO,2CAAyCA,QAAO,0CAA0C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,2CAAyCA,QAAO,0CAA0C,IAAE,WAAU;AAAC,cAAO,2CAAyCA,QAAO,0CAA0C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qDAAmDA,QAAO,oDAAoD,IAAE,WAAU;AAAC,cAAO,qDAAmDA,QAAO,oDAAoD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qDAAmDA,QAAO,oDAAoD,IAAE,WAAU;AAAC,cAAO,qDAAmDA,QAAO,oDAAoD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gDAA8CA,QAAO,+CAA+C,IAAE,WAAU;AAAC,cAAO,gDAA8CA,QAAO,+CAA+C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gDAA8CA,QAAO,+CAA+C,IAAE,WAAU;AAAC,cAAO,gDAA8CA,QAAO,+CAA+C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yDAAuDA,QAAO,wDAAwD,IAAE,WAAU;AAAC,cAAO,yDAAuDA,QAAO,wDAAwD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yDAAuDA,QAAO,wDAAwD,IAAE,WAAU;AAAC,cAAO,yDAAuDA,QAAO,wDAAwD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gDAA8CA,QAAO,+CAA+C,IAAE,WAAU;AAAC,cAAO,gDAA8CA,QAAO,+CAA+C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gDAA8CA,QAAO,+CAA+C,IAAE,WAAU;AAAC,cAAO,gDAA8CA,QAAO,+CAA+C,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uDAAqDA,QAAO,sDAAsD,IAAE,WAAU;AAAC,cAAO,uDAAqDA,QAAO,sDAAsD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uDAAqDA,QAAO,sDAAsD,IAAE,WAAU;AAAC,cAAO,uDAAqDA,QAAO,sDAAsD,IAAEA,QAAO,KAAK,EAAE,GAAG,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,0CAAwCA,QAAO,yCAAyC,IAAE,WAAU;AAAC,cAAO,0CAAwCA,QAAO,yCAAyC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,+BAA6BA,QAAO,8BAA8B,IAAE,WAAU;AAAC,cAAO,+BAA6BA,QAAO,8BAA8B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,+BAA6BA,QAAO,8BAA8B,IAAE,WAAU;AAAC,cAAO,+BAA6BA,QAAO,8BAA8B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gCAA8BA,QAAO,+BAA+B,IAAE,WAAU;AAAC,cAAO,gCAA8BA,QAAO,+BAA+B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gCAA8BA,QAAO,+BAA+B,IAAE,WAAU;AAAC,cAAO,gCAA8BA,QAAO,+BAA+B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gCAA8BA,QAAO,+BAA+B,IAAE,WAAU;AAAC,cAAO,gCAA8BA,QAAO,+BAA+B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gCAA8BA,QAAO,+BAA+B,IAAE,WAAU;AAAC,cAAO,gCAA8BA,QAAO,+BAA+B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gCAA8BA,QAAO,+BAA+B,IAAE,WAAU;AAAC,cAAO,gCAA8BA,QAAO,+BAA+B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gCAA8BA,QAAO,+BAA+B,IAAE,WAAU;AAAC,cAAO,gCAA8BA,QAAO,+BAA+B,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,sCAAoCA,QAAO,qCAAqC,IAAE,WAAU;AAAC,cAAO,sCAAoCA,QAAO,qCAAqC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,0CAAwCA,QAAO,yCAAyC,IAAE,WAAU;AAAC,cAAO,0CAAwCA,QAAO,yCAAyC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,+CAA6CA,QAAO,8CAA8C,IAAE,WAAU;AAAC,cAAO,+CAA6CA,QAAO,8CAA8C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,8CAA4CA,QAAO,6CAA6C,IAAE,WAAU;AAAC,cAAO,8CAA4CA,QAAO,6CAA6C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,8CAA4CA,QAAO,6CAA6C,IAAE,WAAU;AAAC,cAAO,8CAA4CA,QAAO,6CAA6C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,2CAAyCA,QAAO,0CAA0C,IAAE,WAAU;AAAC,cAAO,2CAAyCA,QAAO,0CAA0C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,0CAAwCA,QAAO,yCAAyC,IAAE,WAAU;AAAC,cAAO,0CAAwCA,QAAO,yCAAyC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,0CAAwCA,QAAO,yCAAyC,IAAE,WAAU;AAAC,cAAO,0CAAwCA,QAAO,yCAAyC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,2CAAyCA,QAAO,0CAA0C,IAAE,WAAU;AAAC,cAAO,2CAAyCA,QAAO,0CAA0C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,sCAAoCA,QAAO,qCAAqC,IAAE,WAAU;AAAC,cAAO,sCAAoCA,QAAO,qCAAqC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,+CAA6CA,QAAO,8CAA8C,IAAE,WAAU;AAAC,cAAO,+CAA6CA,QAAO,8CAA8C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,2DAAyDA,QAAO,0DAA0D,IAAE,WAAU;AAAC,cAAO,2DAAyDA,QAAO,0DAA0D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4DAA0DA,QAAO,2DAA2D,IAAE,WAAU;AAAC,cAAO,4DAA0DA,QAAO,2DAA2D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4DAA0DA,QAAO,2DAA2D,IAAE,WAAU;AAAC,cAAO,4DAA0DA,QAAO,2DAA2D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qDAAmDA,QAAO,oDAAoD,IAAE,WAAU;AAAC,cAAO,qDAAmDA,QAAO,oDAAoD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qDAAmDA,QAAO,oDAAoD,IAAE,WAAU;AAAC,cAAO,qDAAmDA,QAAO,oDAAoD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gEAA8DA,QAAO,+DAA+D,IAAE,WAAU;AAAC,cAAO,gEAA8DA,QAAO,+DAA+D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,gEAA8DA,QAAO,+DAA+D,IAAE,WAAU;AAAC,cAAO,gEAA8DA,QAAO,+DAA+D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kEAAgEA,QAAO,iEAAiE,IAAE,WAAU;AAAC,cAAO,kEAAgEA,QAAO,iEAAiE,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kEAAgEA,QAAO,iEAAiE,IAAE,WAAU;AAAC,cAAO,kEAAgEA,QAAO,iEAAiE,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6DAA2DA,QAAO,4DAA4D,IAAE,WAAU;AAAC,cAAO,6DAA2DA,QAAO,4DAA4D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6DAA2DA,QAAO,4DAA4D,IAAE,WAAU;AAAC,cAAO,6DAA2DA,QAAO,4DAA4D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,wDAAsDA,QAAO,uDAAuD,IAAE,WAAU;AAAC,cAAO,wDAAsDA,QAAO,uDAAuD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,wDAAsDA,QAAO,uDAAuD,IAAE,WAAU;AAAC,cAAO,wDAAsDA,QAAO,uDAAuD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kEAAgEA,QAAO,iEAAiE,IAAE,WAAU;AAAC,cAAO,kEAAgEA,QAAO,iEAAiE,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kEAAgEA,QAAO,iEAAiE,IAAE,WAAU;AAAC,cAAO,kEAAgEA,QAAO,iEAAiE,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4DAA0DA,QAAO,2DAA2D,IAAE,WAAU;AAAC,cAAO,4DAA0DA,QAAO,2DAA2D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4DAA0DA,QAAO,2DAA2D,IAAE,WAAU;AAAC,cAAO,4DAA0DA,QAAO,2DAA2D,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qDAAmDA,QAAO,oDAAoD,IAAE,WAAU;AAAC,cAAO,qDAAmDA,QAAO,oDAAoD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qDAAmDA,QAAO,oDAAoD,IAAE,WAAU;AAAC,cAAO,qDAAmDA,QAAO,oDAAoD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,oDAAkDA,QAAO,mDAAmD,IAAE,WAAU;AAAC,cAAO,oDAAkDA,QAAO,mDAAmD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mCAAiCA,QAAO,kCAAkC,IAAE,WAAU;AAAC,cAAO,mCAAiCA,QAAO,kCAAkC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4CAA0CA,QAAO,2CAA2C,IAAE,WAAU;AAAC,cAAO,4CAA0CA,QAAO,2CAA2C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,6CAA2CA,QAAO,4CAA4C,IAAE,WAAU;AAAC,cAAO,6CAA2CA,QAAO,4CAA4C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,wCAAsCA,QAAO,uCAAuC,IAAE,WAAU;AAAC,cAAO,wCAAsCA,QAAO,uCAAuC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mDAAiDA,QAAO,kDAAkD,IAAE,WAAU;AAAC,cAAO,mDAAiDA,QAAO,kDAAkD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4CAA0CA,QAAO,2CAA2C,IAAE,WAAU;AAAC,cAAO,4CAA0CA,QAAO,2CAA2C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4CAA0CA,QAAO,2CAA2C,IAAE,WAAU;AAAC,cAAO,4CAA0CA,QAAO,2CAA2C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,oCAAkCA,QAAO,mCAAmC,IAAE,WAAU;AAAC,cAAO,oCAAkCA,QAAO,mCAAmC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iCAA+BA,QAAO,gCAAgC,IAAE,WAAU;AAAC,cAAO,iCAA+BA,QAAO,gCAAgC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,mCAAiCA,QAAO,kCAAkC,IAAE,WAAU;AAAC,cAAO,mCAAiCA,QAAO,kCAAkC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,oCAAkCA,QAAO,mCAAmC,IAAE,WAAU;AAAC,cAAO,oCAAkCA,QAAO,mCAAmC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kCAAgCA,QAAO,iCAAiC,IAAE,WAAU;AAAC,cAAO,kCAAgCA,QAAO,iCAAiC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4CAA0CA,QAAO,2CAA2C,IAAE,WAAU;AAAC,cAAO,4CAA0CA,QAAO,2CAA2C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,4CAA0CA,QAAO,2CAA2C,IAAE,WAAU;AAAC,cAAO,4CAA0CA,QAAO,2CAA2C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,kDAAgDA,QAAO,iDAAiD,IAAE,WAAU;AAAC,cAAO,kDAAgDA,QAAO,iDAAiD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,qCAAmCA,QAAO,oCAAoC,IAAE,WAAU;AAAC,cAAO,qCAAmCA,QAAO,oCAAoC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,yCAAuCA,QAAO,wCAAwC,IAAE,WAAU;AAAC,cAAO,yCAAuCA,QAAO,wCAAwC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,8CAA4CA,QAAO,6CAA6C,IAAE,WAAU;AAAC,cAAO,8CAA4CA,QAAO,6CAA6C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,8CAA4CA,QAAO,6CAA6C,IAAE,WAAU;AAAC,cAAO,8CAA4CA,QAAO,6CAA6C,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,iDAA+CA,QAAO,gDAAgD,IAAE,WAAU;AAAC,cAAO,iDAA+CA,QAAO,gDAAgD,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,sCAAoCA,QAAO,qCAAqC,IAAE,WAAU;AAAC,cAAO,sCAAoCA,QAAO,qCAAqC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,uCAAqCA,QAAO,sCAAsC,IAAE,WAAU;AAAC,cAAO,uCAAqCA,QAAO,sCAAsC,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,UAAQA,QAAO,SAAS,IAAE,WAAU;AAAC,cAAO,UAAQA,QAAO,SAAS,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,QAAI,QAAMA,QAAO,OAAO,IAAE,WAAU;AAAC,cAAO,QAAMA,QAAO,OAAO,IAAEA,QAAO,KAAK,EAAE,IAAI,GAAG,MAAM,MAAK,SAAS;AAAA,IAAC;AAAE,IAAAA,QAAO,cAAc,IAAE;AAAa,IAAAA,QAAO,aAAa,IAAE;AAAY,QAAI;AAAU,4BAAsB,SAAS,YAAW;AAAC,UAAG,CAAC,UAAU,KAAI;AAAE,UAAG,CAAC,UAAU,yBAAsB;AAAA,IAAS;AAAE,aAAS,IAAI,MAAK;AAAC,aAAK,QAAM;AAAW,UAAG,kBAAgB,GAAE;AAAC;AAAA,MAAM;AAAC,aAAO;AAAE,UAAG,kBAAgB,GAAE;AAAC;AAAA,MAAM;AAAC,eAAS,QAAO;AAAC,YAAG,UAAU;AAAO,oBAAU;AAAK,QAAAA,QAAO,WAAW,IAAE;AAAK,YAAG,MAAM;AAAO,oBAAY;AAAE,4BAAoBA,OAAM;AAAE,YAAGA,QAAO,sBAAsB,EAAE,CAAAA,QAAO,sBAAsB,EAAE;AAAE,gBAAQ;AAAA,MAAC;AAAC,UAAGA,QAAO,WAAW,GAAE;AAAC,QAAAA,QAAO,WAAW,EAAE,YAAY;AAAE,mBAAW,WAAU;AAAC,qBAAW,WAAU;AAAC,YAAAA,QAAO,WAAW,EAAE,EAAE;AAAA,UAAC,GAAE,CAAC;AAAE,gBAAM;AAAA,QAAC,GAAE,CAAC;AAAA,MAAC,OAAK;AAAC,cAAM;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAA,QAAO,KAAK,IAAE;AAAI,QAAGA,QAAO,SAAS,GAAE;AAAC,UAAG,OAAOA,QAAO,SAAS,KAAG,WAAW,CAAAA,QAAO,SAAS,IAAE,CAACA,QAAO,SAAS,CAAC;AAAE,aAAMA,QAAO,SAAS,EAAE,SAAO,GAAE;AAAC,QAAAA,QAAO,SAAS,EAAE,IAAI,EAAE;AAAA,MAAC;AAAA,IAAC;AAAC,QAAI;AAAE,aAAS,gBAAe;AAAA,IAAC;AAAC,kBAAc,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,kBAAc,UAAU,cAAY;AAAc,kBAAc,UAAU,YAAU;AAAc,kBAAc,YAAU,CAAC;AAAE,IAAAA,QAAO,eAAe,IAAE;AAAc,aAAS,SAAS,WAAU;AAAC,cAAO,aAAW,eAAe;AAAA,IAAS;AAAC,IAAAA,QAAO,UAAU,IAAE;AAAS,aAAS,YAAY,KAAI,WAAU;AAAC,UAAI,QAAM,SAAS,SAAS;AAAE,UAAI,MAAI,MAAM,GAAG;AAAE,UAAG,IAAI,QAAO;AAAI,YAAI,OAAO,QAAQ,aAAW,eAAe,SAAS;AAAE,UAAI,MAAI;AAAI,aAAO,MAAM,GAAG,IAAE;AAAA,IAAG;AAAC,IAAAA,QAAO,aAAa,IAAE;AAAY,aAAS,WAAW,KAAI,WAAU;AAAC,aAAO,YAAY,IAAI,KAAI,SAAS;AAAA,IAAC;AAAC,IAAAA,QAAO,YAAY,IAAE;AAAW,IAAAA,QAAO,MAAM,IAAE,YAAY,CAAC;AAAE,aAAS,QAAQ,KAAI;AAAC,UAAG,CAAC,IAAI,aAAa,EAAE,OAAK;AAA8D,UAAI,aAAa,EAAE;AAAE,aAAO,SAAS,IAAI,SAAS,EAAE,IAAI,GAAG;AAAA,IAAC;AAAC,IAAAA,QAAO,SAAS,IAAE;AAAQ,aAAS,QAAQ,MAAK,MAAK;AAAC,aAAO,KAAK,QAAM,KAAK;AAAA,IAAG;AAAC,IAAAA,QAAO,SAAS,IAAE;AAAQ,aAAS,WAAW,KAAI;AAAC,aAAO,IAAI;AAAA,IAAG;AAAC,IAAAA,QAAO,YAAY,IAAE;AAAW,aAAS,SAAS,KAAI;AAAC,aAAO,IAAI;AAAA,IAAS;AAAC,IAAAA,QAAO,UAAU,IAAE;AAAS,QAAI,cAAY,EAAC,QAAO,GAAE,MAAK,GAAE,KAAI,GAAE,OAAM,CAAC,GAAE,QAAO,GAAE,SAAQ,WAAU;AAAC,UAAG,YAAY,QAAO;AAAC,iBAAQ,IAAE,GAAE,IAAE,YAAY,MAAM,QAAO,KAAI;AAAC,UAAAA,QAAO,OAAO,EAAE,YAAY,MAAM,CAAC,CAAC;AAAA,QAAC;AAAC,oBAAY,MAAM,SAAO;AAAE,QAAAA,QAAO,OAAO,EAAE,YAAY,MAAM;AAAE,oBAAY,SAAO;AAAE,oBAAY,QAAM,YAAY;AAAO,oBAAY,SAAO;AAAA,MAAC;AAAC,UAAG,CAAC,YAAY,QAAO;AAAC,oBAAY,QAAM;AAAI,oBAAY,SAAOA,QAAO,SAAS,EAAE,YAAY,IAAI;AAAE,eAAO,YAAY,MAAM;AAAA,MAAC;AAAC,kBAAY,MAAI;AAAA,IAAC,GAAE,OAAM,SAAS,OAAM,MAAK;AAAC,aAAO,YAAY,MAAM;AAAE,UAAI,QAAM,KAAK;AAAkB,UAAI,MAAI,MAAM,SAAO;AAAM,YAAI,MAAI,IAAE;AAAG,UAAI;AAAI,UAAG,YAAY,MAAI,OAAK,YAAY,MAAK;AAAC,eAAO,MAAI,CAAC;AAAE,oBAAY,UAAQ;AAAI,cAAIA,QAAO,SAAS,EAAE,GAAG;AAAE,oBAAY,MAAM,KAAK,GAAG;AAAA,MAAC,OAAK;AAAC,cAAI,YAAY,SAAO,YAAY;AAAI,oBAAY,OAAK;AAAA,MAAG;AAAC,aAAO;AAAA,IAAG,GAAE,MAAK,SAAS,OAAM,MAAK,QAAO;AAAC,kBAAU;AAAE,UAAI,QAAM,KAAK;AAAkB,cAAO,OAAM;AAAA,QAAC,KAAK;AAAE,sBAAU;AAAE;AAAA,QAAM,KAAK;AAAE,sBAAU;AAAE;AAAA,QAAM,KAAK;AAAE,sBAAU;AAAE;AAAA,MAAK;AAAC,eAAQ,IAAE,GAAE,IAAE,MAAM,QAAO,KAAI;AAAC,aAAK,SAAO,CAAC,IAAE,MAAM,CAAC;AAAA,MAAC;AAAA,IAAC,EAAC;AAAE,aAAS,YAAY,OAAM;AAAC,UAAG,OAAO,UAAQ,UAAS;AAAC,YAAI,SAAO,YAAY,MAAM,OAAM,MAAM;AAAE,oBAAY,KAAK,OAAM,QAAO,MAAM;AAAE,eAAO;AAAA,MAAM;AAAC,aAAO;AAAA,IAAK;AAAC,aAAS,cAAc,OAAM;AAAC,UAAG,OAAO,UAAQ,UAAS;AAAC,YAAI,SAAO,YAAY,MAAM,OAAM,OAAO;AAAE,oBAAY,KAAK,OAAM,SAAQ,MAAM;AAAE,eAAO;AAAA,MAAM;AAAC,aAAO;AAAA,IAAK;AAAC,aAAS,UAAS;AAAC,YAAK;AAAA,IAAmD;AAAC,YAAQ,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,YAAQ,UAAU,cAAY;AAAQ,YAAQ,UAAU,YAAU;AAAQ,YAAQ,YAAU,CAAC;AAAE,IAAAA,QAAO,SAAS,IAAE;AAAQ,YAAQ,UAAU,aAAa,IAAE,QAAQ,UAAU,cAAY,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,6CAAuCA,KAAI;AAAA,IAAC;AAAE,aAAS,WAAU;AAAC,WAAK,MAAI,qCAAqC;AAAE,eAAS,QAAQ,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,aAAS,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,aAAS,UAAU,cAAY;AAAS,aAAS,UAAU,YAAU;AAAS,aAAS,YAAU,CAAC;AAAE,IAAAN,QAAO,UAAU,IAAE;AAAS,aAAS,UAAU,WAAW,IAAE,SAAS,UAAU,YAAU,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,aAAO,sCAAsCA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,WAAW,IAAE,SAAS,UAAU,YAAU,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,4CAAsCA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,SAAQ,EAAC,KAAI,SAAS,UAAU,WAAU,KAAI,SAAS,UAAU,UAAS,CAAC;AAAE,aAAS,UAAU,YAAY,IAAE,SAAS,UAAU,aAAW,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,uCAAuCA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,YAAY,IAAE,SAAS,UAAU,aAAW,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,6CAAuCA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,UAAS,EAAC,KAAI,SAAS,UAAU,YAAW,KAAI,SAAS,UAAU,WAAU,CAAC;AAAE,aAAS,UAAU,cAAc,IAAE,SAAS,UAAU,eAAa,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,yCAAyCA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,cAAc,IAAE,SAAS,UAAU,eAAa,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,+CAAyCA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,YAAW,EAAC,KAAI,SAAS,UAAU,cAAa,KAAI,SAAS,UAAU,aAAY,CAAC;AAAE,aAAS,UAAU,gBAAgB,IAAE,SAAS,UAAU,iBAAe,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,2CAA2CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,gBAAgB,IAAE,SAAS,UAAU,iBAAe,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,iDAA2CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,cAAa,EAAC,KAAI,SAAS,UAAU,gBAAe,KAAI,SAAS,UAAU,eAAc,CAAC;AAAE,aAAS,UAAU,QAAQ,IAAE,SAAS,UAAU,SAAO,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,mCAAmCA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,QAAQ,IAAE,SAAS,UAAU,SAAO,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,yCAAmCA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,MAAK,EAAC,KAAI,SAAS,UAAU,QAAO,KAAI,SAAS,UAAU,OAAM,CAAC;AAAE,aAAS,UAAU,QAAQ,IAAE,SAAS,UAAU,SAAO,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,mCAAmCA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,QAAQ,IAAE,SAAS,UAAU,SAAO,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,yCAAmCA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,MAAK,EAAC,KAAI,SAAS,UAAU,QAAO,KAAI,SAAS,UAAU,OAAM,CAAC;AAAE,aAAS,UAAU,UAAU,IAAE,SAAS,UAAU,WAAS,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,aAAO,qCAAqCA,OAAK,IAAI;AAAA,IAAC;AAAE,aAAS,UAAU,UAAU,IAAE,SAAS,UAAU,WAAS,SAAS,MAAK,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,kBAAY,QAAQ;AAAE,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,2CAAqCA,OAAK,MAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,QAAO,EAAC,KAAI,SAAS,UAAU,UAAS,KAAI,SAAS,UAAU,SAAQ,CAAC;AAAE,aAAS,UAAU,UAAU,IAAE,SAAS,UAAU,WAAS,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,aAAO,qCAAqCA,OAAK,IAAI;AAAA,IAAC;AAAE,aAAS,UAAU,UAAU,IAAE,SAAS,UAAU,WAAS,SAAS,MAAK,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,kBAAY,QAAQ;AAAE,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,2CAAqCA,OAAK,MAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,QAAO,EAAC,KAAI,SAAS,UAAU,UAAS,KAAI,SAAS,UAAU,SAAQ,CAAC;AAAE,aAAS,UAAU,wBAAwB,IAAE,SAAS,UAAU,yBAAuB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,mDAAmDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,wBAAwB,IAAE,SAAS,UAAU,yBAAuB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,yDAAmDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,sBAAqB,EAAC,KAAI,SAAS,UAAU,wBAAuB,KAAI,SAAS,UAAU,uBAAsB,CAAC;AAAE,aAAS,UAAU,oBAAoB,IAAE,SAAS,UAAU,qBAAmB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,+CAA+CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,oBAAoB,IAAE,SAAS,UAAU,qBAAmB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,qDAA+CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,kBAAiB,EAAC,KAAI,SAAS,UAAU,oBAAmB,KAAI,SAAS,UAAU,mBAAkB,CAAC;AAAE,aAAS,UAAU,mBAAmB,IAAE,SAAS,UAAU,oBAAkB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,8CAA8CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,mBAAmB,IAAE,SAAS,UAAU,oBAAkB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,oDAA8CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,iBAAgB,EAAC,KAAI,SAAS,UAAU,mBAAkB,KAAI,SAAS,UAAU,kBAAiB,CAAC;AAAE,aAAS,UAAU,oBAAoB,IAAE,SAAS,UAAU,qBAAmB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,+CAA+CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,oBAAoB,IAAE,SAAS,UAAU,qBAAmB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,qDAA+CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,kBAAiB,EAAC,KAAI,SAAS,UAAU,oBAAmB,KAAI,SAAS,UAAU,mBAAkB,CAAC;AAAE,aAAS,UAAU,gBAAgB,IAAE,SAAS,UAAU,iBAAe,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,2CAA2CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,gBAAgB,IAAE,SAAS,UAAU,iBAAe,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,iDAA2CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,cAAa,EAAC,KAAI,SAAS,UAAU,gBAAe,KAAI,SAAS,UAAU,eAAc,CAAC;AAAE,aAAS,UAAU,4BAA4B,IAAE,SAAS,UAAU,6BAA2B,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,uDAAuDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,4BAA4B,IAAE,SAAS,UAAU,6BAA2B,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,6DAAuDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,0BAAyB,EAAC,KAAI,SAAS,UAAU,4BAA2B,KAAI,SAAS,UAAU,2BAA0B,CAAC;AAAE,aAAS,UAAU,mBAAmB,IAAE,SAAS,UAAU,oBAAkB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,8CAA8CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,mBAAmB,IAAE,SAAS,UAAU,oBAAkB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,oDAA8CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,iBAAgB,EAAC,KAAI,SAAS,UAAU,mBAAkB,KAAI,SAAS,UAAU,kBAAiB,CAAC;AAAE,aAAS,UAAU,qBAAqB,IAAE,SAAS,UAAU,sBAAoB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,gDAAgDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,qBAAqB,IAAE,SAAS,UAAU,sBAAoB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,sDAAgDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,mBAAkB,EAAC,KAAI,SAAS,UAAU,qBAAoB,KAAI,SAAS,UAAU,oBAAmB,CAAC;AAAE,aAAS,UAAU,qBAAqB,IAAE,SAAS,UAAU,sBAAoB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,gDAAgDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,qBAAqB,IAAE,SAAS,UAAU,sBAAoB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,sDAAgDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,mBAAkB,EAAC,KAAI,SAAS,UAAU,qBAAoB,KAAI,SAAS,UAAU,oBAAmB,CAAC;AAAE,aAAS,UAAU,sBAAsB,IAAE,SAAS,UAAU,uBAAqB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,iDAAiDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,sBAAsB,IAAE,SAAS,UAAU,uBAAqB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,uDAAiDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,oBAAmB,EAAC,KAAI,SAAS,UAAU,sBAAqB,KAAI,SAAS,UAAU,qBAAoB,CAAC;AAAE,aAAS,UAAU,0BAA0B,IAAE,SAAS,UAAU,2BAAyB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,qDAAqDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAU,0BAA0B,IAAE,SAAS,UAAU,2BAAyB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,2DAAqDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,SAAS,WAAU,wBAAuB,EAAC,KAAI,SAAS,UAAU,0BAAyB,KAAI,SAAS,UAAU,yBAAwB,CAAC;AAAE,aAAS,UAAU,aAAa,IAAE,SAAS,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,8CAAwCA,KAAI;AAAA,IAAC;AAAE,aAAS,KAAK,GAAE,GAAE,GAAE;AAAC,UAAG,KAAG,OAAO,MAAI,SAAS,KAAE,EAAE;AAAI,UAAG,KAAG,OAAO,MAAI,SAAS,KAAE,EAAE;AAAI,UAAG,KAAG,OAAO,MAAI,SAAS,KAAE,EAAE;AAAI,UAAG,MAAI,QAAU;AAAC,aAAK,MAAI,6BAA6B;AAAE,iBAAS,IAAI,EAAE,KAAK,GAAG,IAAE;AAAK;AAAA,MAAM;AAAC,UAAG,MAAI,QAAU;AAAC,aAAK,MAAI,6BAA6B,CAAC;AAAE,iBAAS,IAAI,EAAE,KAAK,GAAG,IAAE;AAAK;AAAA,MAAM;AAAC,UAAG,MAAI,QAAU;AAAC,aAAK,MAAI,6BAA6B,GAAE,CAAC;AAAE,iBAAS,IAAI,EAAE,KAAK,GAAG,IAAE;AAAK;AAAA,MAAM;AAAC,WAAK,MAAI,6BAA6B,GAAE,GAAE,CAAC;AAAE,eAAS,IAAI,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,SAAK,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,SAAK,UAAU,cAAY;AAAK,SAAK,UAAU,YAAU;AAAK,SAAK,YAAU,CAAC;AAAE,IAAAN,QAAO,MAAM,IAAE;AAAK,SAAK,UAAU,OAAO,IAAE,KAAK,UAAU,QAAM,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,aAAO,8BAA8BA,KAAI;AAAA,IAAC;AAAE,SAAK,UAAU,OAAO,IAAE,KAAK,UAAU,QAAM,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,oCAA8BA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,KAAK,WAAU,KAAI,EAAC,KAAI,KAAK,UAAU,OAAM,KAAI,KAAK,UAAU,MAAK,CAAC;AAAE,SAAK,UAAU,OAAO,IAAE,KAAK,UAAU,QAAM,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,8BAA8BA,KAAI;AAAA,IAAC;AAAE,SAAK,UAAU,OAAO,IAAE,KAAK,UAAU,QAAM,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,oCAA8BA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,KAAK,WAAU,KAAI,EAAC,KAAI,KAAK,UAAU,OAAM,KAAI,KAAK,UAAU,MAAK,CAAC;AAAE,SAAK,UAAU,OAAO,IAAE,KAAK,UAAU,QAAM,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,8BAA8BA,KAAI;AAAA,IAAC;AAAE,SAAK,UAAU,OAAO,IAAE,KAAK,UAAU,QAAM,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,oCAA8BA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,KAAK,WAAU,KAAI,EAAC,KAAI,KAAK,UAAU,OAAM,KAAI,KAAK,UAAU,MAAK,CAAC;AAAE,SAAK,UAAU,aAAa,IAAE,KAAK,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,0CAAoCA,KAAI;AAAA,IAAC;AAAE,aAAS,WAAU;AAAC,WAAK,MAAI,qCAAqC;AAAE,eAAS,QAAQ,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,aAAS,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,aAAS,UAAU,cAAY;AAAS,aAAS,UAAU,YAAU;AAAS,aAAS,YAAU,CAAC;AAAE,IAAAN,QAAO,UAAU,IAAE;AAAS,aAAS,UAAU,UAAU,IAAE,SAAS,UAAU,WAAS,SAAS,GAAE;AAAC,UAAIM,QAAK,KAAK;AAAI,UAAG,KAAG,OAAO,MAAI,SAAS,KAAE,EAAE;AAAI,aAAO,YAAY,qCAAqCA,OAAK,CAAC,GAAE,IAAI;AAAA,IAAC;AAAE,aAAS,UAAU,aAAa,IAAE,SAAS,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,8CAAwCA,KAAI;AAAA,IAAC;AAAE,aAAS,eAAc;AAAC,WAAK,MAAI,6CAA6C;AAAE,eAAS,YAAY,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,iBAAa,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,iBAAa,UAAU,cAAY;AAAa,iBAAa,UAAU,YAAU;AAAa,iBAAa,YAAU,CAAC;AAAE,IAAAN,QAAO,cAAc,IAAE;AAAa,iBAAa,UAAU,kBAAkB,IAAE,aAAa,UAAU,mBAAiB,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,aAAO,iDAAiDA,KAAI;AAAA,IAAC;AAAE,iBAAa,UAAU,aAAa,IAAE,aAAa,UAAU,cAAY,SAAS,GAAE;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,KAAG,OAAO,MAAI,SAAS,KAAE,EAAE;AAAI,aAAO,YAAY,4CAA4CA,OAAK,CAAC,GAAE,QAAQ;AAAA,IAAC;AAAE,iBAAa,UAAU,aAAa,IAAE,aAAa,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,kDAA4CA,KAAI;AAAA,IAAC;AAAE,aAAS,YAAW;AAAC,YAAK;AAAA,IAAqD;AAAC,cAAU,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,cAAU,UAAU,cAAY;AAAU,cAAU,UAAU,YAAU;AAAU,cAAU,YAAU,CAAC;AAAE,IAAAN,QAAO,WAAW,IAAE;AAAU,cAAU,UAAU,aAAa,IAAE,UAAU,UAAU,cAAY,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,+CAAyCA,KAAI;AAAA,IAAC;AAAE,aAAS,cAAa;AAAC,WAAK,MAAI,2CAA2C;AAAE,eAAS,WAAW,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,gBAAY,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,gBAAY,UAAU,cAAY;AAAY,gBAAY,UAAU,YAAU;AAAY,gBAAY,YAAU,CAAC;AAAE,IAAAN,QAAO,aAAa,IAAE;AAAY,gBAAY,UAAU,iBAAiB,IAAE,YAAY,UAAU,kBAAgB,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,aAAO,+CAA+CA,KAAI;AAAA,IAAC;AAAE,gBAAY,UAAU,iBAAiB,IAAE,YAAY,UAAU,kBAAgB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,qDAA+CA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,YAAY,WAAU,eAAc,EAAC,KAAI,YAAY,UAAU,iBAAgB,KAAI,YAAY,UAAU,gBAAe,CAAC;AAAE,gBAAY,UAAU,UAAU,IAAE,YAAY,UAAU,WAAS,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,wCAAwCA,KAAI;AAAA,IAAC;AAAE,gBAAY,UAAU,UAAU,IAAE,YAAY,UAAU,WAAS,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,8CAAwCA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,YAAY,WAAU,QAAO,EAAC,KAAI,YAAY,UAAU,UAAS,KAAI,YAAY,UAAU,SAAQ,CAAC;AAAE,gBAAY,UAAU,aAAa,IAAE,YAAY,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,iDAA2CA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAS;AAAC,YAAK;AAAA,IAAmD;AAAC,YAAQ,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,YAAQ,UAAU,cAAY;AAAQ,YAAQ,UAAU,YAAU;AAAQ,YAAQ,YAAU,CAAC;AAAE,IAAAN,QAAO,SAAS,IAAE;AAAQ,YAAQ,UAAU,eAAe,IAAE,QAAQ,UAAU,gBAAc,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,aAAO,yCAAyCA,KAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,UAAU,IAAE,QAAQ,UAAU,WAAS,SAAS,GAAE;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,KAAG,OAAO,MAAI,SAAS,KAAE,EAAE;AAAI,aAAO,YAAY,oCAAoCA,OAAK,CAAC,GAAE,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,aAAa,IAAE,QAAQ,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,6CAAuCA,KAAI;AAAA,IAAC;AAAE,aAAS,gBAAe;AAAC,YAAK;AAAA,IAAyD;AAAC,kBAAc,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,kBAAc,UAAU,cAAY;AAAc,kBAAc,UAAU,YAAU;AAAc,kBAAc,YAAU,CAAC;AAAE,IAAAN,QAAO,eAAe,IAAE;AAAc,kBAAc,UAAU,aAAa,IAAE,cAAc,UAAU,cAAY,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,mDAA6CA,KAAI;AAAA,IAAC;AAAE,aAAS,qBAAoB;AAAC,WAAK,MAAI,yDAAyD;AAAE,eAAS,kBAAkB,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,uBAAmB,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,uBAAmB,UAAU,cAAY;AAAmB,uBAAmB,UAAU,YAAU;AAAmB,uBAAmB,YAAU,CAAC;AAAE,IAAAN,QAAO,oBAAoB,IAAE;AAAmB,uBAAmB,UAAU,YAAY,IAAE,mBAAmB,UAAU,aAAW,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,aAAO,iDAAiDA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,YAAY,IAAE,mBAAmB,UAAU,aAAW,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,uDAAiDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,UAAS,EAAC,KAAI,mBAAmB,UAAU,YAAW,KAAI,mBAAmB,UAAU,WAAU,CAAC;AAAE,uBAAmB,UAAU,YAAY,IAAE,mBAAmB,UAAU,aAAW,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,iDAAiDA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,YAAY,IAAE,mBAAmB,UAAU,aAAW,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,uDAAiDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,UAAS,EAAC,KAAI,mBAAmB,UAAU,YAAW,KAAI,mBAAmB,UAAU,WAAU,CAAC;AAAE,uBAAmB,UAAU,qBAAqB,IAAE,mBAAmB,UAAU,sBAAoB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,0DAA0DA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,qBAAqB,IAAE,mBAAmB,UAAU,sBAAoB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,gEAA0DA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,mBAAkB,EAAC,KAAI,mBAAmB,UAAU,qBAAoB,KAAI,mBAAmB,UAAU,oBAAmB,CAAC;AAAE,uBAAmB,UAAU,cAAc,IAAE,mBAAmB,UAAU,eAAa,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,mDAAmDA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,cAAc,IAAE,mBAAmB,UAAU,eAAa,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,yDAAmDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,YAAW,EAAC,KAAI,mBAAmB,UAAU,cAAa,KAAI,mBAAmB,UAAU,aAAY,CAAC;AAAE,uBAAmB,UAAU,yBAAyB,IAAE,mBAAmB,UAAU,0BAAwB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,8DAA8DA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,yBAAyB,IAAE,mBAAmB,UAAU,0BAAwB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,oEAA8DA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,uBAAsB,EAAC,KAAI,mBAAmB,UAAU,yBAAwB,KAAI,mBAAmB,UAAU,wBAAuB,CAAC;AAAE,uBAAmB,UAAU,2BAA2B,IAAE,mBAAmB,UAAU,4BAA0B,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,gEAAgEA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,2BAA2B,IAAE,mBAAmB,UAAU,4BAA0B,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,sEAAgEA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,yBAAwB,EAAC,KAAI,mBAAmB,UAAU,2BAA0B,KAAI,mBAAmB,UAAU,0BAAyB,CAAC;AAAE,uBAAmB,UAAU,sBAAsB,IAAE,mBAAmB,UAAU,uBAAqB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,2DAA2DA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,sBAAsB,IAAE,mBAAmB,UAAU,uBAAqB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,iEAA2DA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,oBAAmB,EAAC,KAAI,mBAAmB,UAAU,sBAAqB,KAAI,mBAAmB,UAAU,qBAAoB,CAAC;AAAE,uBAAmB,UAAU,iBAAiB,IAAE,mBAAmB,UAAU,kBAAgB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,sDAAsDA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,iBAAiB,IAAE,mBAAmB,UAAU,kBAAgB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,4DAAsDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,eAAc,EAAC,KAAI,mBAAmB,UAAU,iBAAgB,KAAI,mBAAmB,UAAU,gBAAe,CAAC;AAAE,uBAAmB,UAAU,2BAA2B,IAAE,mBAAmB,UAAU,4BAA0B,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,gEAAgEA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,2BAA2B,IAAE,mBAAmB,UAAU,4BAA0B,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,sEAAgEA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,yBAAwB,EAAC,KAAI,mBAAmB,UAAU,2BAA0B,KAAI,mBAAmB,UAAU,0BAAyB,CAAC;AAAE,uBAAmB,UAAU,qBAAqB,IAAE,mBAAmB,UAAU,sBAAoB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,0DAA0DA,KAAI;AAAA,IAAC;AAAE,uBAAmB,UAAU,qBAAqB,IAAE,mBAAmB,UAAU,sBAAoB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,gEAA0DA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,mBAAkB,EAAC,KAAI,mBAAmB,UAAU,qBAAoB,KAAI,mBAAmB,UAAU,oBAAmB,CAAC;AAAE,uBAAmB,UAAU,cAAc,IAAE,mBAAmB,UAAU,eAAa,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,YAAY,mDAAmDA,KAAI,GAAE,OAAO;AAAA,IAAC;AAAE,uBAAmB,UAAU,cAAc,IAAE,mBAAmB,UAAU,eAAa,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,yDAAmDA,OAAK,IAAI;AAAA,IAAC;AAAE,WAAO,eAAe,mBAAmB,WAAU,YAAW,EAAC,KAAI,mBAAmB,UAAU,cAAa,KAAI,mBAAmB,UAAU,aAAY,CAAC;AAAE,uBAAmB,UAAU,aAAa,IAAE,mBAAmB,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,wDAAkDA,KAAI;AAAA,IAAC;AAAE,aAAS,UAAS;AAAC,WAAK,MAAI,mCAAmC;AAAE,eAAS,OAAO,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,YAAQ,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,YAAQ,UAAU,cAAY;AAAQ,YAAQ,UAAU,YAAU;AAAQ,YAAQ,YAAU,CAAC;AAAE,IAAAN,QAAO,SAAS,IAAE;AAAQ,YAAQ,UAAU,SAAS,IAAE,QAAQ,UAAU,UAAQ,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,yCAAmCA,KAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,OAAO,IAAE,QAAQ,UAAU,QAAM,SAAS,WAAU,eAAc,SAAQ,YAAW,QAAO;AAAC,UAAIA,QAAK,KAAK;AAAI,kBAAY,QAAQ;AAAE,UAAG,OAAO,aAAW,UAAS;AAAC,oBAAU,cAAc,SAAS;AAAA,MAAC;AAAC,UAAG,iBAAe,OAAO,kBAAgB,SAAS,iBAAc,cAAc;AAAI,UAAG,OAAO,WAAS,UAAS;AAAC,kBAAQ,YAAY,OAAO;AAAA,MAAC;AAAC,UAAG,cAAY,OAAO,eAAa,SAAS,cAAW,WAAW;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,uCAAiCA,OAAK,WAAU,eAAc,SAAQ,YAAW,MAAM;AAAA,IAAC;AAAE,YAAQ,UAAU,sBAAsB,IAAE,QAAQ,UAAU,uBAAqB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,sDAAgDA,OAAK,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,gBAAgB,IAAE,QAAQ,UAAU,iBAAe,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,YAAY,0CAA0CA,KAAI,GAAE,WAAW;AAAA,IAAC;AAAE,YAAQ,UAAU,iBAAiB,IAAE,QAAQ,UAAU,kBAAgB,SAAS,MAAK;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,QAAM,OAAO,SAAO,SAAS,QAAK,KAAK;AAAI,iDAA2CA,OAAK,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,iBAAiB,IAAE,QAAQ,UAAU,kBAAgB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,YAAY,2CAA2CA,KAAI,GAAE,YAAY;AAAA,IAAC;AAAE,YAAQ,UAAU,iBAAiB,IAAE,QAAQ,UAAU,kBAAgB,SAAS,UAAS;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,aAAO,YAAY,2CAA2CA,OAAK,QAAQ,GAAE,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,sBAAsB,IAAE,QAAQ,UAAU,uBAAqB,SAAS,UAAS,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,UAAG,aAAW,OAAO,cAAY,SAAS,aAAU,UAAU;AAAI,aAAO,YAAY,gDAAgDA,OAAK,UAAS,SAAS,GAAE,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,WAAW,IAAE,QAAQ,UAAU,YAAU,SAAS,UAAS,aAAY;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,UAAG,eAAa,OAAO,gBAAc,SAAS,eAAY,YAAY;AAAI,aAAO,YAAY,qCAAqCA,OAAK,UAAS,WAAW,GAAE,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,YAAY,IAAE,QAAQ,UAAU,aAAW,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,YAAY,sCAAsCA,KAAI,GAAE,SAAS;AAAA,IAAC;AAAE,YAAQ,UAAU,aAAa,IAAE,QAAQ,UAAU,cAAY,SAAS,OAAM,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,SAAO,OAAO,UAAQ,SAAS,SAAM,MAAM;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,YAAY,uCAAuCA,OAAK,OAAM,GAAG,GAAE,OAAO;AAAA,IAAC;AAAE,YAAQ,UAAU,uBAAuB,IAAE,QAAQ,UAAU,wBAAsB,SAAS,QAAO;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,uDAAiDA,OAAK,MAAM;AAAA,IAAC;AAAE,YAAQ,UAAU,uBAAuB,IAAE,QAAQ,UAAU,wBAAsB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,YAAY,iDAAiDA,KAAI,GAAE,IAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,qBAAqB,IAAE,QAAQ,UAAU,sBAAoB,SAAS,UAAS,QAAO,QAAO;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,aAAO,YAAY,+CAA+CA,OAAK,UAAS,QAAO,MAAM,GAAE,aAAa;AAAA,IAAC;AAAE,YAAQ,UAAU,gBAAgB,IAAE,QAAQ,UAAU,iBAAe,SAAS,UAAS,QAAO,OAAM;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,UAAG,SAAO,OAAO,UAAQ,SAAS,SAAM,MAAM;AAAI,aAAO,YAAY,0CAA0CA,OAAK,UAAS,QAAO,KAAK,GAAE,aAAa;AAAA,IAAC;AAAE,YAAQ,UAAU,gBAAgB,IAAE,QAAQ,UAAU,iBAAe,SAAS,UAAS;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,gDAA0CA,OAAK,QAAQ;AAAA,IAAC;AAAE,YAAQ,UAAU,QAAQ,IAAE,QAAQ,UAAU,SAAO,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,wCAAkCA,KAAI;AAAA,IAAC;AAAE,YAAQ,UAAU,aAAa,IAAE,QAAQ,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,6CAAuCA,KAAI;AAAA,IAAC;AAAE,aAAS,MAAM,WAAU,gBAAe,KAAI;AAAC,UAAG,aAAW,OAAO,cAAY,SAAS,aAAU,UAAU;AAAI,UAAG,kBAAgB,OAAO,mBAAiB,SAAS,kBAAe,eAAe;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,WAAK,MAAI,+BAA+B,WAAU,gBAAe,GAAG;AAAE,eAAS,KAAK,EAAE,KAAK,GAAG,IAAE;AAAA,IAAI;AAAC,UAAM,YAAU,OAAO,OAAO,cAAc,SAAS;AAAE,UAAM,UAAU,cAAY;AAAM,UAAM,UAAU,YAAU;AAAM,UAAM,YAAU,CAAC;AAAE,IAAAN,QAAO,OAAO,IAAE;AAAM,UAAM,UAAU,SAAS,IAAE,MAAM,UAAU,UAAQ,WAAU;AAAC,UAAIM,QAAK,KAAK;AAAI,uCAAiCA,KAAI;AAAA,IAAC;AAAE,UAAM,UAAU,UAAU,IAAE,MAAM,UAAU,WAAS,SAAS,UAAS,QAAO;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,YAAU,OAAO,aAAW,SAAS,YAAS,SAAS;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,aAAO,kCAAkCA,OAAK,UAAS,MAAM;AAAA,IAAC;AAAE,UAAM,UAAU,aAAa,IAAE,MAAM,UAAU,cAAY,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,2CAAqCA,OAAK,GAAG;AAAA,IAAC;AAAE,UAAM,UAAU,QAAQ,IAAE,MAAM,UAAU,SAAO,SAAS,IAAG;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,MAAI,OAAO,OAAK,SAAS,MAAG,GAAG;AAAI,sCAAgCA,OAAK,EAAE;AAAA,IAAC;AAAE,UAAM,UAAU,kBAAkB,IAAE,MAAM,UAAU,mBAAiB,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,YAAY,0CAA0CA,OAAK,GAAG,GAAE,IAAI;AAAA,IAAC;AAAE,UAAM,UAAU,kBAAkB,IAAE,MAAM,UAAU,mBAAiB,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,YAAY,0CAA0CA,OAAK,GAAG,GAAE,IAAI;AAAA,IAAC;AAAE,UAAM,UAAU,wBAAwB,IAAE,MAAM,UAAU,yBAAuB,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,YAAY,gDAAgDA,OAAK,GAAG,GAAE,IAAI;AAAA,IAAC;AAAE,UAAM,UAAU,eAAe,IAAE,MAAM,UAAU,gBAAc,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,uCAAuCA,OAAK,GAAG;AAAA,IAAC;AAAE,UAAM,UAAU,uBAAuB,IAAE,MAAM,UAAU,wBAAsB,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAM,CAAC,CAAC,+CAA+CA,OAAK,GAAG;AAAA,IAAC;AAAE,UAAM,UAAU,WAAW,IAAE,MAAM,UAAU,YAAU,SAAS,KAAI,aAAY;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,UAAG,eAAa,OAAO,gBAAc,SAAS,eAAY,YAAY;AAAI,yCAAmCA,OAAK,KAAI,WAAW;AAAA,IAAC;AAAE,UAAM,UAAU,eAAe,IAAE,MAAM,UAAU,gBAAc,SAAS,KAAI,aAAY;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,UAAG,eAAa,OAAO,gBAAc,SAAS,eAAY,YAAY;AAAI,6CAAuCA,OAAK,KAAI,WAAW;AAAA,IAAC;AAAE,UAAM,UAAU,oBAAoB,IAAE,MAAM,UAAU,qBAAmB,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,YAAY,4CAA4CA,OAAK,GAAG,GAAE,kBAAkB;AAAA,IAAC;AAAE,UAAM,UAAU,oBAAoB,IAAE,MAAM,UAAU,qBAAmB,SAAS,KAAI,QAAO;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,kDAA4CA,OAAK,KAAI,MAAM;AAAA,IAAC;AAAE,UAAM,UAAU,uBAAuB,IAAE,MAAM,UAAU,wBAAsB,SAAS,QAAO;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,UAAQ,OAAO,WAAS,SAAS,UAAO,OAAO;AAAI,qDAA+CA,OAAK,MAAM;AAAA,IAAC;AAAE,UAAM,UAAU,uBAAuB,IAAE,MAAM,UAAU,wBAAsB,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,aAAO,YAAY,+CAA+CA,KAAI,GAAE,IAAI;AAAA,IAAC;AAAE,UAAM,UAAU,YAAY,IAAE,MAAM,UAAU,aAAW,SAAS,KAAI;AAAC,UAAIA,QAAK,KAAK;AAAI,UAAG,OAAK,OAAO,QAAM,SAAS,OAAI,IAAI;AAAI,aAAO,YAAY,oCAAoCA,OAAK,GAAG,GAAE,OAAO;AAAA,IAAC;AAAE,UAAM,UAAU,aAAa,IAAE,MAAM,UAAU,cAAY,WAAU;AAAC,UAAIA,QAAK,KAAK;AAAI,2CAAqCA,KAAI;AAAA,IAAC;AAGrn5F,WAAON,QAAO;AAAA,EAChB;AAEA,EAAG;AACH,IAAO,kBAAQ;", "names": ["<PERSON><PERSON><PERSON>", "err", "buffer", "exports", "compare", "date", "self"]}