import { useEffect, useCallback, useRef } from 'react';
import { ariaUtils, focusManagement, motionPreferences, a11yTesting } from '../utils/accessibility';

interface UseAccessibilityOptions {
  announcePageChanges?: boolean;
  enableFocusManagement?: boolean;
  respectMotionPreferences?: boolean;
  enableA11yTesting?: boolean;
}

export const useAccessibility = (options: UseAccessibilityOptions = {}) => {
  const {
    announcePageChanges = true,
    enableFocusManagement = true,
    respectMotionPreferences = true,
    enableA11yTesting = process.env.NODE_ENV === 'development',
  } = options;

  const previousTitle = useRef<string>('');

  // Announce page changes to screen readers
  useEffect(() => {
    if (announcePageChanges) {
      const currentTitle = document.title;
      if (previousTitle.current && previousTitle.current !== currentTitle) {
        ariaUtils.announce(`Page changed to ${currentTitle}`, 'polite');
      }
      previousTitle.current = currentTitle;
    }
  }, [announcePageChanges]);

  // Run accessibility tests in development
  useEffect(() => {
    if (enableA11yTesting) {
      const runTests = () => {
        console.group('🔍 Accessibility Tests');
        
        const altTextCheck = a11yTesting.checkMissingAltText();
        console.log('✅ Alt text check:', altTextCheck ? 'PASS' : 'FAIL');
        
        const headingCheck = a11yTesting.checkHeadingHierarchy();
        console.log('✅ Heading hierarchy check:', headingCheck ? 'PASS' : 'FAIL');
        
        const keyboardCheck = a11yTesting.checkKeyboardAccessibility();
        console.log('✅ Keyboard accessibility check:', keyboardCheck ? 'PASS' : 'FAIL');
        
        console.groupEnd();
      };

      // Run tests after DOM is ready
      const timer = setTimeout(runTests, 1000);
      return () => clearTimeout(timer);
    }
  }, [enableA11yTesting]);

  // Announce messages to screen readers
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    ariaUtils.announce(message, priority);
  }, []);

  // Focus management utilities
  const focus = useCallback({
    trap: (element: HTMLElement) => {
      if (enableFocusManagement) {
        return focusManagement.trapFocus(element);
      }
      return () => {};
    },
    save: () => {
      if (enableFocusManagement) {
        return focusManagement.saveFocus();
      }
      return () => {};
    },
    firstError: (formElement: HTMLElement) => {
      if (enableFocusManagement) {
        focusManagement.focusFirstError(formElement);
      }
    },
  }, [enableFocusManagement]);

  // Motion preference utilities
  const motion = useCallback({
    prefersReduced: () => motionPreferences.prefersReducedMotion(),
    respectPreference: (normalDuration: number, reducedDuration: number = 0) => {
      if (respectMotionPreferences) {
        return motionPreferences.respectMotionPreference(normalDuration, reducedDuration);
      }
      return normalDuration;
    },
  }, [respectMotionPreferences]);

  // Generate unique IDs for ARIA relationships
  const generateId = useCallback((prefix?: string) => {
    return ariaUtils.generateId(prefix);
  }, []);

  // Keyboard event handlers
  const handleKeyboard = useCallback({
    escape: (callback: () => void) => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault();
          callback();
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    },
    
    enter: (callback: () => void) => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Enter') {
          event.preventDefault();
          callback();
        }
      };

      return handleKeyDown;
    },

    space: (callback: () => void) => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === ' ') {
          event.preventDefault();
          callback();
        }
      };

      return handleKeyDown;
    },
  }, []);

  // ARIA state management
  const aria = useCallback({
    setExpanded: (element: HTMLElement, expanded: boolean) => {
      ariaUtils.setExpanded(element, expanded);
    },
    setSelected: (element: HTMLElement, selected: boolean) => {
      ariaUtils.setSelected(element, selected);
    },
    setPressed: (element: HTMLElement, pressed: boolean) => {
      ariaUtils.setPressed(element, pressed);
    },
  }, []);

  return {
    announce,
    focus,
    motion,
    generateId,
    handleKeyboard,
    aria,
  };
};
