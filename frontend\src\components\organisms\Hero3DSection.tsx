import React, { Suspense, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { Button } from '../atoms';
import Scene3D from '../atoms/Scene3D';

// Lazy load Spline for better performance
const Spline = React.lazy(() => import('@splinetool/react-spline'));

interface Hero3DSectionProps {
  className?: string;
}

const Hero3DSection: React.FC<Hero3DSectionProps> = ({ className }) => {
  const [splineLoaded, setSplineLoaded] = useState(false);
  const [splineError, setSplineError] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const navigate = useNavigate();

  // Mouse parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSplineLoad = () => {
    setSplineLoaded(true);
  };

  const handleSplineError = () => {
    setSplineError(true);
    console.warn('Spline failed to load, using React Three Fiber fallback');
  };

  const handleGetStarted = () => {
    navigate('/contact');
  };

  const handleExploreServices = () => {
    navigate('/services');
  };

  return (
    <section
      className={cn(
        'relative min-h-screen flex items-center overflow-hidden',
        'bg-black', // Dark background to match the animation
        className
      )}
    >
      {/* Full-Screen 3D Background Animation */}
      <div
        className="absolute inset-0 z-0"
        style={{
          transform: `translate3d(${mousePosition.x * 2}px, ${mousePosition.y * 2}px, 0)`,
          transition: 'transform 0.1s ease-out',
        }}
      >
        {/* Primary 3D Scene - Spline Full Screen */}
        {!splineError && (
          <Suspense
            fallback={
              <div className="w-full h-full flex items-center justify-center bg-black">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-400 mx-auto mb-4"></div>
                  <p className="text-primary-400 font-medium">Loading 3D Experience...</p>
                </div>
              </div>
            }
          >
            <div className={cn(
              'w-full h-full transition-opacity duration-1000',
              splineLoaded ? 'opacity-100' : 'opacity-0'
            )}>
              <Spline
                scene={import.meta.env.VITE_SPLINE_SCENE_URL}
                onLoad={handleSplineLoad}
                onError={handleSplineError}
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'absolute',
                  top: 0,
                  left: 0
                }}
              />
            </div>
          </Suspense>
        )}

        {/* Fallback 3D Scene - React Three Fiber */}
        {splineError && (
          <div className="w-full h-full bg-black">
            <Scene3D
              interactive={true}
              mousePosition={mousePosition}
              className="opacity-80"
            />
          </div>
        )}
      </div>

      {/* Content Overlay - Positioned on Right Side */}
      <div className="relative z-10 w-full min-h-screen flex items-center">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 w-full items-center min-h-screen">

            {/* Empty Space for Animation - Left Side */}
            <div className="hidden lg:block lg:col-span-6 xl:col-span-7">
              {/* This space is intentionally left for the background animation */}
            </div>

            {/* Content Section - Right Side */}
            <div className="lg:col-span-6 xl:col-span-5 z-10 relative flex items-center justify-center lg:justify-start min-h-screen lg:min-h-0">
              <div
                className="space-y-6 lg:space-y-8 p-6 lg:p-8 rounded-2xl w-full max-w-2xl mx-auto lg:mx-0"
                style={{
                  transform: `translateY(${scrollY * 0.2}px)`,
                  background: 'rgba(0, 0, 0, 0.6)', // Slightly more opaque on mobile for better readability
                  backdropFilter: 'blur(20px)',
                  WebkitBackdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}
              >
                {/* Main Headline */}
                <div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-heading font-bold text-white mb-4 sm:mb-6 leading-tight">
                    <span className="block">Transform Your</span>
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400">
                      Digital Future
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl lg:text-lg xl:text-xl text-gray-200 leading-relaxed mb-6 lg:mb-8">
                    We craft immersive digital experiences and intelligent automation solutions
                    that propel your business into the future.
                  </p>
                </div>

                {/* Call-to-Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 lg:gap-6">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handleGetStarted}
                    className="w-full sm:w-auto shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white border-0"
                  >
                    Start Your Project
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleExploreServices}
                    className="w-full sm:w-auto border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 transition-all duration-300"
                  >
                    Explore Services
                  </Button>
                </div>

                {/* Key Features Highlight */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 lg:gap-6 mt-8 lg:mt-12">
                  <div className="bg-black/40 backdrop-blur-sm p-4 lg:p-6 rounded-xl shadow-lg border border-white/10 hover:border-cyan-400/30 transition-all duration-300">
                    <div className="w-10 h-10 lg:w-12 lg:h-12 bg-cyan-500/20 rounded-lg flex items-center justify-center mb-3 lg:mb-4">
                      <svg className="w-5 h-5 lg:w-6 lg:h-6 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-base lg:text-lg font-semibold text-white mb-2">Full-Stack Development</h3>
                    <p className="text-gray-300 text-sm">End-to-end solutions from concept to deployment</p>
                  </div>

                  <div className="bg-black/40 backdrop-blur-sm p-4 lg:p-6 rounded-xl shadow-lg border border-white/10 hover:border-blue-400/30 transition-all duration-300">
                    <div className="w-10 h-10 lg:w-12 lg:h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-3 lg:mb-4">
                      <svg className="w-5 h-5 lg:w-6 lg:h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <h3 className="text-base lg:text-lg font-semibold text-white mb-2">Smart Automation</h3>
                    <p className="text-gray-300 text-sm">Intelligent workflows that boost productivity</p>
                  </div>

                  <div className="bg-black/40 backdrop-blur-sm p-4 lg:p-6 rounded-xl shadow-lg border border-white/10 hover:border-purple-400/30 transition-all duration-300">
                    <div className="w-10 h-10 lg:w-12 lg:h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-3 lg:mb-4">
                      <svg className="w-5 h-5 lg:w-6 lg:h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <h3 className="text-base lg:text-lg font-semibold text-white mb-2">Innovation-Driven</h3>
                    <p className="text-gray-300 text-sm">Cutting-edge technologies for competitive advantage</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 animate-bounce">
        <button
          onClick={() => {
            window.scrollBy({ top: window.innerHeight, behavior: 'smooth' });
          }}
          className="p-3 rounded-full bg-black/40 backdrop-blur-sm hover:bg-black/60 transition-all duration-200 border border-white/20 hover:border-cyan-400/50"
          aria-label="Continue to next section"
        >
          <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      </div>

      {/* Floating Elements for Additional Visual Interest */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden z-5">
        <div
          className="absolute top-1/4 left-1/8 w-2 h-2 bg-cyan-400 rounded-full opacity-60 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 20}px, ${mousePosition.y * 20}px)`,
          }}
        />
        <div
          className="absolute top-3/4 right-1/8 w-3 h-3 bg-blue-400 rounded-full opacity-40 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * -15}px, ${mousePosition.y * -15}px)`,
            animationDelay: '1s',
          }}
        />
        <div
          className="absolute top-1/2 left-1/6 w-1 h-1 bg-purple-400 rounded-full opacity-80 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 25}px, ${mousePosition.y * 25}px)`,
            animationDelay: '2s',
          }}
        />
      </div>
    </section>
  );
};

export default Hero3DSection;
