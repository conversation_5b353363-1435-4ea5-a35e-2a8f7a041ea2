import React, { Suspense, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { Button } from '../atoms';
import Scene3D from '../atoms/Scene3D';

// Lazy load Spline for better performance
const Spline = React.lazy(() => import('@splinetool/react-spline'));

interface Hero3DSectionProps {
  className?: string;
}

const Hero3DSection: React.FC<Hero3DSectionProps> = ({ className }) => {
  const [splineLoaded, setSplineLoaded] = useState(false);
  const [splineError, setSplineError] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const navigate = useNavigate();

  // Mouse parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSplineLoad = () => {
    setSplineLoaded(true);
  };

  const handleSplineError = () => {
    setSplineError(true);
    console.warn('Spline failed to load, using React Three Fiber fallback');
  };

  const handleGetStarted = () => {
    navigate('/contact');
  };

  const handleExploreServices = () => {
    navigate('/services');
  };

  return (
    <section
      className={cn(
        'relative min-h-screen flex items-center overflow-hidden',
        'bg-black', // Dark background to match the animation
        className
      )}
    >
      {/* Full-Screen 3D Foreground Animation */}
      <div
        className="absolute inset-0 z-20 w-full h-full pointer-events-auto"
        style={{
          transform: `translate3d(${mousePosition.x * 2}px, ${mousePosition.y * 2}px, 0)`,
          transition: 'transform 0.1s ease-out',
        }}
      >
        {/* Primary 3D Scene - Spline Full Screen Foreground */}
        {!splineError && (
          <Suspense
            fallback={
              <div className="w-full h-full flex items-center justify-center bg-transparent">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-400 mx-auto mb-4"></div>
                  <p className="text-primary-400 font-medium">Loading 3D Experience...</p>
                </div>
              </div>
            }
          >
            <div className={cn(
              'w-full h-full transition-opacity duration-1000',
              splineLoaded ? 'opacity-100' : 'opacity-0'
            )}>
              <Spline
                scene={import.meta.env.VITE_SPLINE_SCENE_URL}
                onLoad={handleSplineLoad}
                onError={handleSplineError}
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  pointerEvents: 'auto'
                }}
              />
            </div>
          </Suspense>
        )}

        {/* Fallback 3D Scene - React Three Fiber Foreground */}
        {splineError && (
          <div className="w-full h-full bg-transparent">
            <Scene3D
              interactive={true}
              mousePosition={mousePosition}
              className="opacity-90"
            />
          </div>
        )}
      </div>

      {/* Content Background - Positioned Behind 3D */}
      <div className="absolute inset-0 z-0 w-full h-screen flex items-center bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="w-full max-w-none px-6 lg:px-12 xl:px-16 2xl:px-20">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 w-full items-center h-full">

            {/* Content Section - Left Side Background */}
            <div className="lg:col-span-6 xl:col-span-5 relative flex items-center justify-center lg:justify-start h-full">
              <div className="w-full max-w-2xl">
              <div
                className="space-y-8 lg:space-y-10 p-8 lg:p-10 xl:p-12 rounded-3xl w-full max-w-4xl mx-auto lg:mx-0"
                style={{
                  transform: `translateY(${scrollY * 0.2}px)`,
                  background: 'rgba(0, 0, 0, 0.8)',
                  backdropFilter: 'blur(20px)',
                  WebkitBackdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}
              >
                {/* Main Headline */}
                <div>
                  <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-heading font-bold text-white mb-6 sm:mb-8 leading-tight">
                    <span className="block">Effortless</span>
                    <span className="block">AI integration</span>
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">
                      for business
                    </span>
                  </h1>
                  <p className="text-xl sm:text-2xl lg:text-xl xl:text-2xl text-gray-300 leading-relaxed mb-8 lg:mb-10 max-w-xl">
                    No extra set-up, just smart automation when you need it and the heavy lifting when you stay in control.
                  </p>
                </div>

                {/* Call-to-Action Button */}
                <div className="flex justify-start">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handleGetStarted}
                    className="px-10 py-4 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-teal-600 to-cyan-600 hover:from-teal-700 hover:to-cyan-700 text-white border-0 font-semibold text-lg"
                  >
                    JOIN US NOW
                  </Button>
                </div>


              </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
        <button
          onClick={() => {
            window.scrollBy({ top: window.innerHeight, behavior: 'smooth' });
          }}
          className="p-3 rounded-full bg-black/40 backdrop-blur-sm hover:bg-black/60 transition-all duration-200 border border-white/20 hover:border-cyan-400/50"
          aria-label="Continue to next section"
        >
          <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      </div>

      {/* Floating Elements for Additional Visual Interest */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden z-25">
        <div
          className="absolute top-1/4 left-1/8 w-2 h-2 bg-cyan-400 rounded-full opacity-60 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 20}px, ${mousePosition.y * 20}px)`,
          }}
        />
        <div
          className="absolute top-3/4 right-1/8 w-3 h-3 bg-blue-400 rounded-full opacity-40 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * -15}px, ${mousePosition.y * -15}px)`,
            animationDelay: '1s',
          }}
        />
        <div
          className="absolute top-1/2 left-1/6 w-1 h-1 bg-purple-400 rounded-full opacity-80 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 25}px, ${mousePosition.y * 25}px)`,
            animationDelay: '2s',
          }}
        />
      </div>
    </section>
  );
};

export default Hero3DSection;
