import React, { Suspense, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { Button } from '../atoms';
import Scene3D from '../atoms/Scene3D';

// Lazy load Spline for better performance
const Spline = React.lazy(() => import('@splinetool/react-spline'));

interface Hero3DSectionProps {
  className?: string;
}

const Hero3DSection: React.FC<Hero3DSectionProps> = ({ className }) => {
  const [splineLoaded, setSplineLoaded] = useState(false);
  const [splineError, setSplineError] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const navigate = useNavigate();

  // Mouse parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSplineLoad = () => {
    setSplineLoaded(true);
  };

  const handleSplineError = () => {
    setSplineError(true);
    console.warn('Spline failed to load, using React Three Fiber fallback');
  };

  const handleGetStarted = () => {
    navigate('/contact');
  };

  const handleExploreServices = () => {
    navigate('/services');
  };

  return (
    <section 
      className={cn(
        'relative min-h-screen flex items-center justify-center overflow-hidden',
        'bg-gradient-to-br from-primary-50 via-white to-secondary-50',
        className
      )}
    >
      {/* 3D Background Layer */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          transform: `translate3d(${mousePosition.x * 10}px, ${mousePosition.y * 10}px, 0) translateY(${scrollY * 0.5}px)`,
          transition: 'transform 0.1s ease-out',
        }}
      >
        {/* Primary 3D Scene - Spline */}
        {!splineError && (
          <Suspense 
            fallback={
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
                  <p className="text-primary-600 font-medium">Loading 3D Experience...</p>
                </div>
              </div>
            }
          >
            <div className={cn(
              'w-full h-full transition-opacity duration-1000',
              splineLoaded ? 'opacity-100' : 'opacity-0'
            )}>
              <Spline
                scene={import.meta.env.VITE_SPLINE_SCENE_URL}
                onLoad={handleSplineLoad}
                onError={handleSplineError}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </Suspense>
        )}
        
        {/* Fallback 3D Scene - React Three Fiber */}
        {splineError && (
          <div className="w-full h-full">
            <Scene3D 
              interactive={true}
              mousePosition={mousePosition}
              className="opacity-80"
            />
          </div>
        )}
        
        {/* Gradient overlay for text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/30" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 container mx-auto px-4 text-center">
        <div className="max-w-5xl mx-auto">
          {/* Main Headline */}
          <div 
            className="mb-8"
            style={{
              transform: `translateY(${scrollY * 0.2}px)`,
            }}
          >
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-heading font-bold text-secondary-900 mb-4 sm:mb-6 leading-tight">
              <span className="block">Transform Your</span>
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-accent-600">
                Digital Future
              </span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed px-2 sm:px-4 md:px-0">
              We craft immersive digital experiences and intelligent automation solutions
              that propel your business into the future.
            </p>
          </div>

          {/* Call-to-Action Buttons */}
          <div
            className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-8 sm:mb-12 px-4 sm:px-0"
            style={{
              transform: `translateY(${scrollY * 0.1}px)`,
            }}
          >
            <Button
              variant="primary"
              size="lg"
              onClick={handleGetStarted}
              className="w-full sm:w-auto sm:min-w-[200px] shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              Start Your Project
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={handleExploreServices}
              className="w-full sm:w-auto sm:min-w-[200px] border-2 hover:bg-primary-50"
            >
              Explore Services
            </Button>
          </div>

          {/* Key Features Highlight */}
          <div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-4xl mx-auto px-4 sm:px-0"
            style={{
              transform: `translateY(${scrollY * 0.05}px)`,
            }}
          >
            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Full-Stack Development</h3>
              <p className="text-secondary-600 text-sm">End-to-end solutions from concept to deployment</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
              <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Smart Automation</h3>
              <p className="text-secondary-600 text-sm">Intelligent workflows that boost productivity</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Innovation-Driven</h3>
              <p className="text-secondary-600 text-sm">Cutting-edge technologies for competitive advantage</p>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 animate-bounce">
        <button
          onClick={() => {
            window.scrollBy({ top: window.innerHeight, behavior: 'smooth' });
          }}
          className="p-3 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-all duration-200 border border-white/30"
          aria-label="Continue to next section"
        >
          <svg className="h-6 w-6 text-secondary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      </div>

      {/* Floating Elements for Additional Visual Interest */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div 
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-primary-400 rounded-full opacity-60 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 20}px, ${mousePosition.y * 20}px)`,
          }}
        />
        <div 
          className="absolute top-3/4 right-1/4 w-3 h-3 bg-accent-400 rounded-full opacity-40 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * -15}px, ${mousePosition.y * -15}px)`,
            animationDelay: '1s',
          }}
        />
        <div 
          className="absolute top-1/2 right-1/3 w-1 h-1 bg-secondary-400 rounded-full opacity-80 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 25}px, ${mousePosition.y * 25}px)`,
            animationDelay: '2s',
          }}
        />
      </div>
    </section>
  );
};

export default Hero3DSection;
