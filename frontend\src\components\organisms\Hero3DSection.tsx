import React, { Suspense, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { Button } from '../atoms';
import Scene3D from '../atoms/Scene3D';

// Lazy load Spline for better performance
const Spline = React.lazy(() => import('@splinetool/react-spline'));

interface Hero3DSectionProps {
  className?: string;
}

const Hero3DSection: React.FC<Hero3DSectionProps> = ({ className }) => {
  const [splineLoaded, setSplineLoaded] = useState(false);
  const [splineError, setSplineError] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const navigate = useNavigate();

  // Mouse parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSplineLoad = () => {
    setSplineLoaded(true);
  };

  const handleSplineError = () => {
    setSplineError(true);
    console.warn('Spline failed to load, using React Three Fiber fallback');
  };

  const handleGetStarted = () => {
    navigate('/contact');
  };



  return (
    <section
      className={cn(
        'relative min-h-screen flex items-center overflow-hidden',
        'bg-black', // Dark background to match the animation
        className
      )}
    >
      {/* Full-Screen 3D Foreground Animation */}
      <div
        className="absolute inset-0 z-20 w-full h-full pointer-events-auto"
        style={{
          transform: `translate3d(${mousePosition.x * 2}px, ${mousePosition.y * 2}px, 0)`,
          transition: 'transform 0.1s ease-out',
        }}
      >
        {/* Primary 3D Scene - Spline Full Screen Foreground */}
        {!splineError && (
          <Suspense
            fallback={
              <div className="w-full h-full flex items-center justify-center bg-transparent">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-400 mx-auto mb-4"></div>
                  <p className="text-primary-400 font-medium">Loading 3D Experience...</p>
                </div>
              </div>
            }
          >
            <div className={cn(
              'w-full h-full transition-opacity duration-1000',
              splineLoaded ? 'opacity-100' : 'opacity-0'
            )}>
              <Spline
                scene={import.meta.env.VITE_SPLINE_SCENE_URL}
                onLoad={handleSplineLoad}
                onError={handleSplineError}
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  pointerEvents: 'auto'
                }}
              />
            </div>
          </Suspense>
        )}

        {/* Fallback 3D Scene - React Three Fiber Foreground */}
        {splineError && (
          <div className="w-full h-full bg-transparent">
            <Scene3D
              interactive={true}
              mousePosition={mousePosition}
              className="opacity-90"
            />
          </div>
        )}
      </div>

      {/* Content Background - Positioned Behind 3D */}
      <div className="absolute inset-0 z-0 w-full h-screen flex items-center bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="w-full max-w-none px-6 lg:px-12 xl:px-16 2xl:px-20">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 w-full items-center h-full">

            {/* Content Section - Left Side Background */}
            <div className="lg:col-span-6 xl:col-span-5 relative flex items-center justify-center lg:justify-start h-full">
              <div className="w-full max-w-2xl">
              <div
                className="group relative space-y-8 lg:space-y-10 p-8 lg:p-10 xl:p-12 rounded-3xl w-full max-w-4xl mx-auto lg:mx-0 transition-all duration-700 hover:scale-[1.02] hover:shadow-2xl hover:shadow-cyan-500/20"
                style={{
                  transform: `translateY(${scrollY * 0.2}px)`,
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                  backdropFilter: 'blur(30px) saturate(180%)',
                  WebkitBackdropFilter: 'blur(30px) saturate(180%)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
                }}
              >
                {/* Glassmorphism glow effect */}
                <div
                  className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                  style={{
                    background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(59, 130, 246, 0.1) 50%, rgba(147, 51, 234, 0.1) 100%)',
                    filter: 'blur(1px)',
                  }}
                />

                {/* Content container */}
                <div className="relative z-10">
                {/* Main Headline */}
                <div>
                  <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-heading font-bold text-white mb-6 sm:mb-8 leading-tight">
                    <span className="block">Effortless</span>
                    <span className="block">AI integration</span>
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">
                      for business
                    </span>
                  </h1>
                  <p className="text-xl sm:text-2xl lg:text-xl xl:text-2xl text-gray-300 leading-relaxed mb-8 lg:mb-10 max-w-xl">
                    No extra set-up, just smart automation when you need it and the heavy lifting when you stay in control.
                  </p>
                </div>

                {/* Call-to-Action Button */}
                <div className="flex justify-start">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handleGetStarted}
                    className="group relative px-10 py-4 shadow-lg hover:shadow-xl transition-all duration-500 bg-gradient-to-r from-teal-600 to-cyan-600 hover:from-teal-500 hover:to-cyan-500 text-white border-0 font-semibold text-lg overflow-hidden"
                  >
                    <span className="relative z-10">JOIN US NOW</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-teal-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  </Button>
                </div>

                {/* Feature Cards with Glassmorphism */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-12">
                  {[
                    {
                      icon: "🚀",
                      title: "AI-Powered",
                      description: "Smart automation that learns and adapts to your business needs",
                      gradient: "from-blue-500/20 to-cyan-500/20",
                      hoverGradient: "from-blue-400/30 to-cyan-400/30"
                    },
                    {
                      icon: "⚡",
                      title: "Lightning Fast",
                      description: "Optimized performance with cutting-edge technology stack",
                      gradient: "from-purple-500/20 to-pink-500/20",
                      hoverGradient: "from-purple-400/30 to-pink-400/30"
                    },
                    {
                      icon: "🛡️",
                      title: "Enterprise Ready",
                      description: "Scalable solutions built for mission-critical applications",
                      gradient: "from-emerald-500/20 to-teal-500/20",
                      hoverGradient: "from-emerald-400/30 to-teal-400/30"
                    }
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="group relative p-6 rounded-2xl transition-all duration-700 hover:scale-105 hover:-translate-y-2 cursor-pointer"
                      style={{
                        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                        backdropFilter: 'blur(20px) saturate(180%)',
                        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                        border: '1px solid rgba(255, 255, 255, 0.15)',
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                      }}
                    >
                      {/* Card glow effect */}
                      <div
                        className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-700 bg-gradient-to-br ${feature.hoverGradient}`}
                        style={{ filter: 'blur(1px)' }}
                      />

                      {/* Card content */}
                      <div className="relative z-10">
                        <div className="text-3xl mb-4 transform group-hover:scale-110 transition-transform duration-500">
                          {feature.icon}
                        </div>
                        <h3 className="text-lg font-semibold text-white mb-3 group-hover:text-cyan-300 transition-colors duration-300">
                          {feature.title}
                        </h3>
                        <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                          {feature.description}
                        </p>
                      </div>

                      {/* Subtle border glow */}
                      <div className="absolute inset-0 rounded-2xl border border-transparent group-hover:border-cyan-400/30 transition-all duration-700" />
                    </div>
                  ))}
                </div>
                </div>
              </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Enhanced Scroll Indicator with Glassmorphism */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
        <div className="flex flex-col items-center space-y-4">
          {/* Scroll text */}
          <div className="text-white/70 text-sm font-medium tracking-wider animate-pulse">
            SCROLL TO EXPLORE
          </div>

          {/* Glassmorphism scroll button */}
          <button
            onClick={() => {
              window.scrollBy({ top: window.innerHeight, behavior: 'smooth' });
            }}
            className="group relative p-4 rounded-full transition-all duration-700 hover:scale-110 animate-bounce"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
              backdropFilter: 'blur(20px) saturate(180%)',
              WebkitBackdropFilter: 'blur(20px) saturate(180%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
            }}
            aria-label="Continue to next section"
          >
            {/* Button glow effect */}
            <div
              className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"
              style={{
                background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%)',
                filter: 'blur(1px)',
              }}
            />

            <svg className="h-6 w-6 text-white relative z-10 group-hover:text-cyan-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>

          {/* Scroll progress line */}
          <div className="w-px h-16 bg-gradient-to-b from-white/30 to-transparent" />
        </div>
      </div>

      {/* Enhanced Floating Elements with Glassmorphism */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden z-25">
        {/* Floating orbs with glassmorphism */}
        <div
          className="absolute top-1/4 left-1/8 w-16 h-16 rounded-full opacity-30 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 20}px, ${mousePosition.y * 20}px) rotate(${scrollY * 0.1}deg)`,
            background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(6, 182, 212, 0.3)',
          }}
        />
        <div
          className="absolute top-3/4 right-1/8 w-12 h-12 rounded-full opacity-25 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * -15}px, ${mousePosition.y * -15}px) rotate(${scrollY * -0.15}deg)`,
            animationDelay: '1s',
            background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.3) 0%, rgba(236, 72, 153, 0.3) 100%)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)',
            boxShadow: '0 8px 32px rgba(147, 51, 234, 0.3)',
          }}
        />
        <div
          className="absolute top-1/2 left-1/6 w-8 h-8 rounded-full opacity-40 animate-pulse"
          style={{
            transform: `translate(${mousePosition.x * 25}px, ${mousePosition.y * 25}px) rotate(${scrollY * 0.2}deg)`,
            animationDelay: '2s',
            background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.3) 0%, rgba(6, 182, 212, 0.3) 100%)',
            backdropFilter: 'blur(6px)',
            WebkitBackdropFilter: 'blur(6px)',
            boxShadow: '0 8px 32px rgba(16, 185, 129, 0.3)',
          }}
        />

        {/* Geometric shapes */}
        <div
          className="absolute top-1/3 right-1/4 w-6 h-6 opacity-20 animate-spin"
          style={{
            transform: `translate(${mousePosition.x * -10}px, ${mousePosition.y * -10}px)`,
            background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
            clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
            animationDuration: '8s',
          }}
        />
        <div
          className="absolute bottom-1/3 left-1/3 w-4 h-4 opacity-25 animate-bounce"
          style={{
            transform: `translate(${mousePosition.x * 15}px, ${mousePosition.y * 15}px)`,
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
            borderRadius: '2px',
            animationDelay: '0.5s',
            animationDuration: '3s',
          }}
        />
      </div>
    </section>
  );
};

export default Hero3DSection;
