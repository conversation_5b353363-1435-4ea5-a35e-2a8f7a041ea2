import React, { useState } from 'react';
import { cn } from '../../utils';
import { GlassButton, GlassCard, GlassSection } from '../atoms';

interface ServicesSectionProps {
  className?: string;
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ className }) => {
  const [activeService, setActiveService] = useState(0);

  const services = [
    {
      id: 'fullstack',
      title: 'Full-Stack Web Development',
      shortDescription: 'Complete web application development from concept to deployment',
      icon: (
        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      features: [
        'React.js & Next.js Frontend Development',
        'Node.js & Python Backend Systems',
        'Database Design & Optimization',
        'RESTful & GraphQL API Development',
        'Cloud Deployment & DevOps',
        'Performance Optimization',
        'Security Implementation',
        'Testing & Quality Assurance'
      ],
      technologies: ['React', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS', 'Docker'],
      details: 'We build scalable, high-performance web applications using modern technologies and best practices. From responsive frontends to robust backends, we handle every aspect of your web development needs.'
    },
    {
      id: 'automation',
      title: 'Make.com Automation',
      shortDescription: 'Streamline business processes with powerful automation workflows',
      icon: (
        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      features: [
        'Workflow Design & Implementation',
        'API Integration & Data Sync',
        'Email & Notification Automation',
        'CRM & Marketing Automation',
        'E-commerce Process Automation',
        'Data Processing & Analytics',
        'Custom Webhook Development',
        'Monitoring & Optimization'
      ],
      technologies: ['Make.com', 'Zapier', 'APIs', 'Webhooks', 'JSON', 'OAuth'],
      details: 'Transform your business operations with intelligent automation. We design and implement make.com workflows that save time, reduce errors, and scale your business processes efficiently.'
    },
    {
      id: 'consulting',
      title: 'Technical Consulting',
      shortDescription: 'Strategic guidance for your technology decisions and architecture',
      icon: (
        <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      features: [
        'Technology Stack Selection',
        'Architecture Design & Review',
        'Performance Audits',
        'Security Assessments',
        'Scalability Planning',
        'Code Reviews & Best Practices',
        'Team Training & Mentoring',
        'Project Planning & Strategy'
      ],
      technologies: ['Architecture', 'Strategy', 'Best Practices', 'Performance', 'Security', 'Scalability'],
      details: 'Get expert guidance on your technology decisions. Our consulting services help you choose the right technologies, design scalable architectures, and implement best practices for long-term success.'
    }
  ];

  const handleGetQuote = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <GlassSection variant="dark" padding="xl" particles>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Services</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            We offer comprehensive technology solutions that cover every aspect of modern web development
            and business automation. Our expertise spans the full technology stack.
          </p>
        </div>

        {/* Services Navigation */}
        <div className="flex flex-wrap justify-center mb-12">
          {services.map((service, index) => (
            <GlassButton
              key={service.id}
              variant={activeService === index ? 'accent' : 'outline'}
              size="md"
              onClick={() => setActiveService(index)}
              className="mx-2 mb-4"
            >
              {service.title}
            </GlassButton>
          ))}
        </div>

        {/* Active Service Details */}
        <GlassCard variant="primary" size="xl" hover glow>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Service Info */}
            <div className="p-8 lg:p-12">
              <div className="flex items-center mb-6">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-cyan-400/20 to-teal-400/20 text-cyan-400 rounded-lg mr-4 backdrop-blur-sm border border-cyan-400/30">
                  {services[activeService].icon}
                </div>
                <h3 className="text-2xl font-bold text-white">
                  {services[activeService].title}
                </h3>
              </div>

              <p className="text-gray-300 mb-8 leading-relaxed">
                {services[activeService].details}
              </p>

              {/* Technologies */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-white mb-4">
                  Technologies We Use
                </h4>
                <div className="flex flex-wrap gap-2">
                  {services[activeService].technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gradient-to-r from-cyan-400/20 to-teal-400/20 text-cyan-400 rounded-full text-sm font-medium backdrop-blur-sm border border-cyan-400/30"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              <GlassButton
                variant="accent"
                size="lg"
                onClick={handleGetQuote}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                Get a Quote
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </GlassButton>
            </div>

            {/* Right Column - Features List */}
            <div className="bg-gradient-to-br from-white/5 to-white/10 p-8 lg:p-12 backdrop-blur-sm border border-white/20 rounded-xl">
              <h4 className="text-xl font-semibold text-white mb-6">
                What's Included
              </h4>
              <ul className="space-y-3">
                {services[activeService].features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-5 w-5 text-cyan-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </GlassCard>

        {/* Call-to-Action */}
        <div className="text-center mt-16">
          <GlassCard variant="secondary" size="lg" hover glow>
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-lg text-gray-300 mb-6">
              Let's discuss how our full-stack development and automation expertise can accelerate your growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GlassButton
                variant="accent"
                size="lg"
                onClick={handleGetQuote}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                Start Your Project
              </GlassButton>
              <GlassButton
                variant="outline"
                size="lg"
                onClick={() => {
                  const caseStudiesSection = document.getElementById('case-studies');
                  if (caseStudiesSection) {
                    caseStudiesSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="transform hover:scale-110 transition-transform duration-300"
              >
                View Case Studies
              </GlassButton>
            </div>
          </GlassCard>
        </div>
      </div>
    </GlassSection>
  );
};

export default ServicesSection;
