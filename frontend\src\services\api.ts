import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ContactFormData, ContactFormResponse, ApiResponse } from '../types';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log('Response received:', response.status);
        return response;
      },
      (error) => {
        console.error('Response error:', error);
        
        // Handle different error scenarios
        if (error.response) {
          // Server responded with error status
          const message = error.response.data?.message || 'Server error occurred';
          throw new Error(message);
        } else if (error.request) {
          // Request was made but no response received
          throw new Error('Network error - please check your connection');
        } else {
          // Something else happened
          throw new Error('An unexpected error occurred');
        }
      }
    );
  }

  /**
   * Submit contact form data
   */
  async submitContactForm(data: ContactFormData): Promise<ContactFormResponse> {
    try {
      const endpoint = import.meta.env.VITE_CONTACT_API_ENDPOINT || '/api/contact';
      const response = await this.client.post<ApiResponse<ContactFormResponse>>(
        endpoint,
        data
      );

      return {
        success: true,
        message: response.data.message || 'Message sent successfully!',
        data: response.data.data,
      };
    } catch (error) {
      console.error('Contact form submission error:', error);
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send message',
      };
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.get('/health');
      return true;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Generic GET request
   */
  async get<T>(url: string): Promise<T> {
    const response = await this.client.get<T>(url);
    return response.data;
  }

  /**
   * Generic POST request
   */
  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.post<T>(url, data);
    return response.data;
  }

  /**
   * Generic PUT request
   */
  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.put<T>(url, data);
    return response.data;
  }

  /**
   * Generic DELETE request
   */
  async delete<T>(url: string): Promise<T> {
    const response = await this.client.delete<T>(url);
    return response.data;
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
