import { useEffect, useCallback, useRef } from 'react';

interface PerformanceMetrics {
  fcp: number | null; // First Contentful Paint
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  ttfb: number | null; // Time to First Byte
}

interface PerformanceConfig {
  enableLogging?: boolean;
  enableReporting?: boolean;
  reportingEndpoint?: string;
  sampleRate?: number;
}

export const usePerformanceMonitor = (config: PerformanceConfig = {}) => {
  const {
    enableLogging = false,
    enableReporting = false,
    reportingEndpoint = '/api/analytics/performance',
    sampleRate = 1.0,
  } = config;

  const metricsRef = useRef<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null,
  });

  const reportMetrics = useCallback(async (metrics: Partial<PerformanceMetrics>) => {
    if (!enableReporting || Math.random() > sampleRate) return;

    try {
      await fetch(reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metrics,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      });
    } catch (error) {
      if (enableLogging) {
        console.warn('Failed to report performance metrics:', error);
      }
    }
  }, [enableReporting, reportingEndpoint, sampleRate, enableLogging]);

  const logMetric = useCallback((name: string, value: number) => {
    if (enableLogging) {
      console.log(`Performance Metric - ${name}: ${value.toFixed(2)}ms`);
    }
  }, [enableLogging]);

  // Measure Core Web Vitals
  useEffect(() => {
    // First Contentful Paint
    const measureFCP = () => {
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry;
      if (fcpEntry) {
        const fcp = fcpEntry.startTime;
        metricsRef.current.fcp = fcp;
        logMetric('FCP', fcp);
        reportMetrics({ fcp });
      }
    };

    // Largest Contentful Paint
    const measureLCP = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          if (lastEntry) {
            const lcp = lastEntry.startTime;
            metricsRef.current.lcp = lcp;
            logMetric('LCP', lcp);
            reportMetrics({ lcp });
          }
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        return () => observer.disconnect();
      }
    };

    // First Input Delay
    const measureFID = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            const fid = entry.processingStart - entry.startTime;
            metricsRef.current.fid = fid;
            logMetric('FID', fid);
            reportMetrics({ fid });
          });
        });
        observer.observe({ entryTypes: ['first-input'] });
        return () => observer.disconnect();
      }
    };

    // Cumulative Layout Shift
    const measureCLS = () => {
      if ('PerformanceObserver' in window) {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          metricsRef.current.cls = clsValue;
          logMetric('CLS', clsValue);
          reportMetrics({ cls: clsValue });
        });
        observer.observe({ entryTypes: ['layout-shift'] });
        return () => observer.disconnect();
      }
    };

    // Time to First Byte
    const measureTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        metricsRef.current.ttfb = ttfb;
        logMetric('TTFB', ttfb);
        reportMetrics({ ttfb });
      }
    };

    // Initialize measurements
    const cleanup: (() => void)[] = [];

    // Wait for page load to measure FCP and TTFB
    if (document.readyState === 'complete') {
      measureFCP();
      measureTTFB();
    } else {
      window.addEventListener('load', () => {
        measureFCP();
        measureTTFB();
      });
    }

    // Start observing other metrics
    const lcpCleanup = measureLCP();
    const fidCleanup = measureFID();
    const clsCleanup = measureCLS();

    if (lcpCleanup) cleanup.push(lcpCleanup);
    if (fidCleanup) cleanup.push(fidCleanup);
    if (clsCleanup) cleanup.push(clsCleanup);

    return () => {
      cleanup.forEach(fn => fn());
    };
  }, [logMetric, reportMetrics]);

  // Resource timing monitoring
  useEffect(() => {
    const monitorResources = () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const slowResources = resources.filter(resource => resource.duration > 1000);
      
      if (slowResources.length > 0 && enableLogging) {
        console.warn('Slow resources detected:', slowResources.map(r => ({
          name: r.name,
          duration: r.duration,
          size: r.transferSize,
        })));
      }
    };

    const timer = setTimeout(monitorResources, 5000);
    return () => clearTimeout(timer);
  }, [enableLogging]);

  // Memory usage monitoring (if available)
  useEffect(() => {
    const monitorMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
        };

        if (enableLogging) {
          console.log('Memory Usage:', {
            used: `${(memoryUsage.used / 1024 / 1024).toFixed(2)} MB`,
            total: `${(memoryUsage.total / 1024 / 1024).toFixed(2)} MB`,
            limit: `${(memoryUsage.limit / 1024 / 1024).toFixed(2)} MB`,
          });
        }

        // Report if memory usage is high
        if (memoryUsage.used / memoryUsage.limit > 0.8) {
          reportMetrics({ 
            cls: memoryUsage.used / memoryUsage.limit // Reusing cls field for memory ratio
          });
        }
      }
    };

    const interval = setInterval(monitorMemory, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [enableLogging, reportMetrics]);

  return {
    metrics: metricsRef.current,
    reportCustomMetric: (name: string, value: number) => {
      logMetric(name, value);
      reportMetrics({ [name]: value } as any);
    },
  };
};
