import React from 'react';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <section className="section-padding bg-gradient-to-br from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-heading font-bold mb-6">
              About AptiBot
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 leading-relaxed">
              We're passionate about creating innovative digital solutions that transform 
              businesses and drive meaningful growth.
            </p>
          </div>
        </div>
      </section>

      {/* Our Mission & Vision */}
      <section className="section-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-8">
                  Our Mission
                </h2>
                <p className="text-xl text-secondary-600 leading-relaxed mb-6">
                  To empower businesses with cutting-edge technology solutions that streamline operations, 
                  enhance productivity, and unlock new opportunities for growth.
                </p>
                <p className="text-lg text-secondary-600 leading-relaxed">
                  We believe that every business, regardless of size, deserves access to enterprise-grade 
                  technology solutions that can help them compete and thrive in today's digital landscape.
                </p>
              </div>
              
              <div className="bg-primary-50 p-8 rounded-2xl">
                <h3 className="text-2xl font-semibold text-primary-900 mb-6">Our Vision</h3>
                <p className="text-lg text-primary-700 leading-relaxed mb-6">
                  To be the leading provider of full-stack development and automation solutions, 
                  known for our innovation, reliability, and commitment to client success.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-primary-800 font-medium">Innovation-driven solutions</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-primary-800 font-medium">Client-centric approach</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-primary-800 font-medium">Sustainable growth</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Philosophy & Approach */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Our Philosophy & Approach
              </h2>
              <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                We believe in building lasting partnerships with our clients through transparency, 
                collaboration, and a commitment to excellence.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Quality First */}
              <div className="bg-white p-8 rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Quality First</h3>
                <p className="text-secondary-600 leading-relaxed">
                  We never compromise on quality. Every line of code, every design element, 
                  and every user interaction is crafted with meticulous attention to detail.
                </p>
              </div>

              {/* Agile Methodology */}
              <div className="bg-white p-8 rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Agile Methodology</h3>
                <p className="text-secondary-600 leading-relaxed">
                  We embrace agile development practices, ensuring rapid iteration, 
                  continuous feedback, and adaptive solutions that evolve with your needs.
                </p>
              </div>

              {/* Future-Proof Solutions */}
              <div className="bg-white p-8 rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Future-Proof Solutions</h3>
                <p className="text-secondary-600 leading-relaxed">
                  We build with tomorrow in mind, using modern technologies and scalable 
                  architectures that grow with your business.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="section-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Our Story
              </h2>
              <p className="text-xl text-secondary-600 leading-relaxed">
                Founded with a vision to democratize access to enterprise-grade technology solutions.
              </p>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-lg text-secondary-600 leading-relaxed mb-6">
                AptiBot was born from a simple observation: too many businesses were struggling with 
                outdated systems, manual processes, and technology solutions that didn't scale with 
                their growth. We saw an opportunity to bridge this gap by providing accessible, 
                high-quality development and automation services.
              </p>
              
              <p className="text-lg text-secondary-600 leading-relaxed mb-6">
                Our journey began with a commitment to excellence and a passion for solving complex 
                problems through innovative technology. We've grown from a small team of dedicated 
                developers to a comprehensive solution provider, but our core values remain unchanged: 
                quality, integrity, and client success.
              </p>
              
              <p className="text-lg text-secondary-600 leading-relaxed">
                Today, we're proud to serve businesses of all sizes, from startups looking to build 
                their first digital presence to established enterprises seeking to modernize their 
                operations. Every project we undertake is an opportunity to make a meaningful impact 
                on our clients' success.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Our Core Values
              </h2>
              <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                These principles guide everything we do and shape how we work with our clients and each other.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">Integrity</h3>
                <p className="text-secondary-600">Honest, transparent communication in all our interactions.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">Excellence</h3>
                <p className="text-secondary-600">Striving for the highest standards in everything we deliver.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">Collaboration</h3>
                <p className="text-secondary-600">Working together to achieve shared goals and success.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">Innovation</h3>
                <p className="text-secondary-600">Embracing new technologies and creative problem-solving.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-primary-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Ready to Work Together?
          </h2>
          <p className="text-xl text-primary-100 max-w-2xl mx-auto leading-relaxed mb-8">
            Let's discuss how our expertise and values can help drive your business forward.
          </p>
          <a 
            href="/contact" 
            className="btn-secondary bg-white text-primary-600 hover:bg-primary-50"
          >
            Get in Touch
          </a>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
