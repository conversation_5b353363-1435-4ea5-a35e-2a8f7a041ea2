import React from 'react';
import { GlassCard, GlassButton, GlassSection } from '../components/atoms';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Glassmorphism Hero Section */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-heading font-bold mb-6 text-white leading-tight">
              About <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">AptiBot</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              We're passionate about creating innovative digital solutions that transform
              businesses and drive meaningful growth.
            </p>
          </div>
        </div>
      </GlassSection>

      {/* Our Mission & Vision with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <GlassCard variant="primary" size="lg" hover glow>
                <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-8">
                  Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Mission</span>
                </h2>
                <p className="text-xl text-gray-300 leading-relaxed mb-6">
                  To empower businesses with cutting-edge technology solutions that streamline operations,
                  enhance productivity, and unlock new opportunities for growth.
                </p>
                <p className="text-lg text-gray-300 leading-relaxed">
                  We believe that every business, regardless of size, deserves access to enterprise-grade
                  technology solutions that can help them compete and thrive in today's digital landscape.
                </p>
              </GlassCard>

              <GlassCard variant="accent" size="lg" hover glow>
                <h3 className="text-2xl font-semibold text-white mb-6 flex items-center space-x-2">
                  <span>🎯</span>
                  <span>Our Vision</span>
                </h3>
                <p className="text-lg text-gray-300 leading-relaxed mb-6">
                  To be the leading provider of full-stack development and automation solutions,
                  known for our innovation, reliability, and commitment to client success.
                </p>
                <div className="space-y-4">
                  {[
                    { icon: "💡", text: "Innovation-driven solutions" },
                    { icon: "🤝", text: "Client-centric approach" },
                    { icon: "📈", text: "Sustainable growth" }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <span className="text-xl">{item.icon}</span>
                      <span className="text-white font-medium">{item.text}</span>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Our Philosophy & Approach with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Our Philosophy & <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-cyan-400">Approach</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                We believe in building lasting partnerships with our clients through transparency,
                collaboration, and a commitment to excellence.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: "✨",
                  title: "Quality First",
                  description: "We never compromise on quality. Every line of code, every design element, and every user interaction is crafted with meticulous attention to detail.",
                  variant: "primary" as const
                },
                {
                  icon: "⚡",
                  title: "Agile Methodology",
                  description: "We embrace agile development practices, ensuring rapid iteration, continuous feedback, and adaptive solutions that evolve with your needs.",
                  variant: "secondary" as const
                },
                {
                  icon: "🚀",
                  title: "Future-Proof Solutions",
                  description: "We build with tomorrow in mind, using modern technologies and scalable architectures that grow with your business.",
                  variant: "accent" as const
                }
              ].map((approach, index) => (
                <GlassCard
                  key={index}
                  variant={approach.variant}
                  size="lg"
                  hover
                  glow
                  className="group text-center"
                >
                  <div className="text-4xl mb-6 transform group-hover:scale-110 transition-transform duration-500">
                    {approach.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                    {approach.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                    {approach.description}
                  </p>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Our Story with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">Story</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed">
                Founded with a vision to democratize access to enterprise-grade technology solutions.
              </p>
            </div>

            <GlassCard variant="secondary" size="xl" hover glow>
              <div className="space-y-6">
                <p className="text-lg text-gray-300 leading-relaxed">
                  AptiBot was born from a simple observation: too many businesses were struggling with
                  outdated systems, manual processes, and technology solutions that didn't scale with
                  their growth. We saw an opportunity to bridge this gap by providing accessible,
                  high-quality development and automation services.
                </p>

                <p className="text-lg text-gray-300 leading-relaxed">
                  Our journey began with a commitment to excellence and a passion for solving complex
                  problems through innovative technology. We've grown from a small team of dedicated
                  developers to a comprehensive solution provider, but our core values remain unchanged:
                  quality, integrity, and client success.
                </p>

                <p className="text-lg text-gray-300 leading-relaxed">
                  Today, we're proud to serve businesses of all sizes, from startups looking to build
                  their first digital presence to established enterprises seeking to modernize their
                  operations. Every project we undertake is an opportunity to make a meaningful impact
                  on our clients' success.
                </p>
              </div>
            </GlassCard>
          </div>
        </div>
      </GlassSection>

      {/* Core Values with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Our Core <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">Values</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                These principles guide everything we do and shape how we work with our clients and each other.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: "💎",
                  title: "Integrity",
                  description: "Honest, transparent communication in all our interactions.",
                  variant: "primary" as const
                },
                {
                  icon: "🏆",
                  title: "Excellence",
                  description: "Striving for the highest standards in everything we deliver.",
                  variant: "secondary" as const
                },
                {
                  icon: "🤝",
                  title: "Collaboration",
                  description: "Working together to achieve shared goals and success.",
                  variant: "accent" as const
                },
                {
                  icon: "💡",
                  title: "Innovation",
                  description: "Embracing new technologies and creative problem-solving.",
                  variant: "primary" as const
                }
              ].map((value, index) => (
                <GlassCard
                  key={index}
                  variant={value.variant}
                  size="md"
                  hover
                  glow
                  className="group text-center"
                >
                  <div className="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-500">
                    {value.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors duration-300">
                    {value.title}
                  </h3>
                  <p className="text-gray-300 text-sm group-hover:text-gray-200 transition-colors duration-300">
                    {value.description}
                  </p>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Call to Action with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4 text-center">
          <GlassCard variant="primary" size="xl" hover glow className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
              Ready to Work <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">Together?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8">
              Let's discuss how our expertise and values can help drive your business forward.
            </p>
            <GlassButton
              variant="accent"
              size="lg"
              onClick={() => window.location.href = '/contact'}
              className="transform hover:scale-110 transition-transform duration-300"
            >
              Get in Touch
            </GlassButton>
          </GlassCard>
        </div>
      </GlassSection>
    </div>
  );
};

export default AboutPage;
