import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere, MeshDistortMaterial, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { useDeviceOptimizations } from '../../hooks/useResponsive';

interface AnimatedSphereProps {
  position: [number, number, number];
  color: string;
  speed: number;
}

const AnimatedSphere: React.FC<AnimatedSphereProps> = ({ position, color, speed }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * speed;
      meshRef.current.rotation.y = state.clock.elapsedTime * speed * 0.5;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * speed) * 0.1;
    }
  });

  return (
    <Sphere ref={meshRef} position={position} args={[1, 32, 32]}>
      <MeshDistortMaterial
        color={color}
        attach="material"
        distort={0.3}
        speed={2}
        roughness={0.1}
        metalness={0.8}
      />
    </Sphere>
  );
};

interface FloatingParticlesProps {
  count: number;
}

const FloatingParticles: React.FC<FloatingParticlesProps> = ({ count }) => {
  const meshRef = useRef<THREE.InstancedMesh>(null);
  
  const particles = useMemo(() => {
    const temp = [];
    for (let i = 0; i < count; i++) {
      temp.push({
        position: [
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20,
        ],
        scale: Math.random() * 0.5 + 0.1,
        speed: Math.random() * 0.02 + 0.01,
      });
    }
    return temp;
  }, [count]);

  useFrame((state) => {
    if (meshRef.current) {
      particles.forEach((particle, i) => {
        const matrix = new THREE.Matrix4();
        matrix.setPosition(
          particle.position[0] + Math.sin(state.clock.elapsedTime * particle.speed) * 2,
          particle.position[1] + Math.cos(state.clock.elapsedTime * particle.speed) * 2,
          particle.position[2]
        );
        matrix.scale(new THREE.Vector3(particle.scale, particle.scale, particle.scale));
        meshRef.current!.setMatrixAt(i, matrix);
      });
      meshRef.current.instanceMatrix.needsUpdate = true;
    }
  });

  return (
    <instancedMesh ref={meshRef} args={[undefined, undefined, count]}>
      <sphereGeometry args={[0.1, 8, 8]} />
      <meshBasicMaterial color="#0284c7" transparent opacity={0.6} />
    </instancedMesh>
  );
};

interface Scene3DProps {
  className?: string;
  interactive?: boolean;
  mousePosition?: { x: number; y: number };
}

const Scene3D: React.FC<Scene3DProps> = ({
  className = '',
  interactive = true,
  mousePosition = { x: 0, y: 0 }
}) => {
  const { threeDComplexity, shouldReduceMotion } = useDeviceOptimizations();

  // Adjust complexity based on device capabilities
  const particleCount = threeDComplexity === 'low' ? 20 : threeDComplexity === 'medium' ? 35 : 50;
  const sphereCount = threeDComplexity === 'low' ? 2 : 3;

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
        dpr={threeDComplexity === 'low' ? [1, 1] : [1, 2]}
        performance={{ min: threeDComplexity === 'low' ? 0.3 : 0.5 }}
        frameloop={shouldReduceMotion ? 'demand' : 'always'}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />

        {/* Environment for reflections */}
        <Environment preset="city" />

        {/* Main animated spheres - positioned for better visibility */}
        <AnimatedSphere position={[-1.5, 0, 0]} color="#0284c7" speed={shouldReduceMotion ? 0.1 : 0.5} />
        {sphereCount > 2 && <AnimatedSphere position={[1.5, 0.8, -0.8]} color="#d946ef" speed={shouldReduceMotion ? 0.1 : 0.3} />}
        {sphereCount > 1 && <AnimatedSphere position={[0, -0.8, 0.8]} color="#06b6d4" speed={shouldReduceMotion ? 0.1 : 0.7} />}

        {/* Floating particles - adaptive count */}
        <FloatingParticles count={particleCount} />

        {/* Interactive controls */}
        {interactive && (
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={false}
            autoRotateSpeed={0}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 2}
          />
        )}

        {/* Mouse interaction effect */}
        <group
          rotation={[
            mousePosition.y * 0.1,
            mousePosition.x * 0.1,
            0
          ]}
        >
          <Sphere position={[0, 0, 0]} args={[0.5, 32, 32]}>
            <MeshDistortMaterial
              color="#ffffff"
              attach="material"
              distort={0.1}
              speed={1}
              roughness={0}
              metalness={1}
              transparent
              opacity={0.3}
            />
          </Sphere>
        </group>
      </Canvas>
    </div>
  );
};

export default Scene3D;
