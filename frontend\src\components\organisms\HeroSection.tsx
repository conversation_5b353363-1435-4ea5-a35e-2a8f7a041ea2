import React, { Suspense, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { Button } from '../atoms';

// Lazy load Spline component for better performance
const Spline = React.lazy(() => import('@splinetool/react-spline'));

interface HeroSectionProps {
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  const [splineLoaded, setSplineLoaded] = useState(false);
  const [splineError, setSplineError] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/contact');
  };

  const handleViewWork = () => {
    navigate('/services');
  };

  // Mouse parallax effect for enhanced interactivity
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleSplineLoad = () => {
    setSplineLoaded(true);
  };

  const handleSplineError = () => {
    setSplineError(true);
    console.warn('Spline scene failed to load, falling back to gradient background');
  };

  return (
    <section 
      id="home" 
      className={cn(
        'relative min-h-screen flex items-center justify-center overflow-hidden',
        className
      )}
    >
      {/* Background - Spline 3D Scene or Gradient Fallback with Parallax */}
      <div
        className="absolute inset-0 z-0"
        style={{
          transform: `translate3d(${mousePosition.x * 5}px, ${mousePosition.y * 5}px, 0)`,
          transition: 'transform 0.1s ease-out',
        }}
      >
        {!splineError && (
          <Suspense 
            fallback={
              <div className="w-full h-full bg-gradient-to-br from-primary-50 via-accent-50 to-secondary-50 animate-pulse" />
            }
          >
            <div className={cn(
              'w-full h-full transition-opacity duration-1000',
              splineLoaded ? 'opacity-100' : 'opacity-0'
            )}>
              <Spline
                scene={import.meta.env.VITE_SPLINE_SCENE_URL || "https://prod.spline.design/6Wq1Q7YGyM-iab9i/scene.splinecode"}
                onLoad={handleSplineLoad}
                onError={handleSplineError}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </Suspense>
        )}
        
        {/* Fallback gradient background */}
        <div 
          className={cn(
            'absolute inset-0 bg-gradient-to-br from-primary-50 via-accent-50 to-secondary-50',
            !splineError && splineLoaded && 'opacity-0',
            'transition-opacity duration-1000'
          )}
        />
        
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/10" />
      </div>

      {/* Content */}
      <div className="relative z-10 container-custom">
        <div className="text-center max-w-5xl mx-auto">
          {/* Main Headline */}
          <h1 className="heading-xl text-secondary-900 mb-6 animate-fade-in">
            Transform Your Ideas Into
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-accent-600">
              Powerful Digital Experiences
            </span>
          </h1>
          
          {/* Subheadline */}
          <p className="text-body text-secondary-600 max-w-3xl mx-auto mb-8 animate-slide-up">
            AptiBot delivers premium <strong>full-stack web development</strong> and 
            <strong> make.com automation</strong> solutions. We combine cutting-edge technology 
            with creative innovation to build scalable, high-performance applications that drive results.
          </p>
          
          {/* Key Value Props */}
          <div className="flex flex-wrap justify-center gap-4 mb-10 animate-slide-up">
            <div className="flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-sm">
              <svg className="h-5 w-5 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium text-secondary-700">Full-Stack Expertise</span>
            </div>
            <div className="flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-sm">
              <svg className="h-5 w-5 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span className="text-sm font-medium text-secondary-700">Make.com Automation</span>
            </div>
            <div className="flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-sm">
              <svg className="h-5 w-5 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium text-secondary-700">Rapid Delivery</span>
            </div>
          </div>
          
          {/* Call-to-Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-scale-in">
            <Button
              variant="primary"
              size="lg"
              onClick={handleGetStarted}
              className="shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              Get Started Today
              <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={handleViewWork}
              className="bg-white/80 backdrop-blur-sm border-secondary-300 hover:bg-white hover:border-primary-600"
            >
              View Our Work
            </Button>
          </div>
          
          {/* Trust Indicators */}
          <div className="mt-12 animate-fade-in">
            <p className="text-sm text-secondary-500 mb-4">Trusted by innovative companies</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {/* Placeholder for client logos */}
              <div className="h-8 w-24 bg-secondary-300 rounded opacity-50"></div>
              <div className="h-8 w-20 bg-secondary-300 rounded opacity-50"></div>
              <div className="h-8 w-28 bg-secondary-300 rounded opacity-50"></div>
              <div className="h-8 w-22 bg-secondary-300 rounded opacity-50"></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Scroll Indicator - Updated for landing page scroll narrative */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 animate-bounce">
        <button
          onClick={() => {
            // Scroll to the next section on the landing page
            window.scrollBy({ top: window.innerHeight, behavior: 'smooth' });
          }}
          className="p-2 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-colors duration-200"
          aria-label="Continue to next section"
        >
          <svg className="h-6 w-6 text-secondary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      </div>
    </section>
  );
};

export default HeroSection;
