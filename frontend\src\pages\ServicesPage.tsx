import React from 'react';

const ServicesPage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <section className="section-padding bg-gradient-to-br from-primary-600 to-primary-800 text-white safe-area-top">
        <div className="container mx-auto mobile-padding">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-heading font-bold mb-4 sm:mb-6">
              Our Services
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-primary-100 leading-relaxed px-4 sm:px-0">
              Comprehensive full-stack solutions designed to transform your business
              and accelerate your digital growth.
            </p>
          </div>
        </div>
      </section>

      {/* Full-Stack Web Development */}
      <section className="section-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Full-Stack Web Development
              </h2>
              <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                End-to-end web solutions from concept to deployment, built with modern 
                technologies and best practices.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Frontend Development */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Frontend Development</h3>
                <p className="text-secondary-600 mb-4">
                  Modern, responsive user interfaces built with React, TypeScript, and cutting-edge frameworks.
                </p>
                <ul className="text-sm text-secondary-600 space-y-2">
                  <li>• React & TypeScript</li>
                  <li>• Responsive Design</li>
                  <li>• Performance Optimization</li>
                  <li>• Accessibility Compliance</li>
                </ul>
              </div>

              {/* Backend Development */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">Backend Development</h3>
                <p className="text-secondary-600 mb-4">
                  Robust server-side solutions with APIs, databases, and cloud infrastructure.
                </p>
                <ul className="text-sm text-secondary-600 space-y-2">
                  <li>• RESTful APIs</li>
                  <li>• Database Design</li>
                  <li>• Cloud Infrastructure</li>
                  <li>• Security Implementation</li>
                </ul>
              </div>

              {/* DevOps & Deployment */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-secondary-200">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">DevOps & Deployment</h3>
                <p className="text-secondary-600 mb-4">
                  Streamlined deployment pipelines and infrastructure management for scalable applications.
                </p>
                <ul className="text-sm text-secondary-600 space-y-2">
                  <li>• CI/CD Pipelines</li>
                  <li>• Cloud Deployment</li>
                  <li>• Monitoring & Analytics</li>
                  <li>• Performance Optimization</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Automation Services */}
      <section className="section-padding bg-secondary-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-secondary-900 mb-6">
                Business Process Automation
              </h2>
              <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                Streamline your operations with intelligent automation solutions that save time, 
                reduce errors, and boost productivity.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-semibold text-secondary-900 mb-6">
                  Transform Your Workflow
                </h3>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-secondary-900 mb-2">Data Integration</h4>
                      <p className="text-secondary-600">Connect disparate systems and automate data flow between applications.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-secondary-900 mb-2">Workflow Automation</h4>
                      <p className="text-secondary-600">Automate repetitive tasks and create efficient business processes.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-secondary-900 mb-2">Custom Solutions</h4>
                      <p className="text-secondary-600">Tailored automation solutions designed specifically for your business needs.</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-lg">
                <h4 className="text-xl font-semibold text-secondary-900 mb-6">Popular Automation Use Cases</h4>
                <div className="space-y-4">
                  <div className="border-l-4 border-primary-600 pl-4">
                    <h5 className="font-semibold text-secondary-900">Lead Management</h5>
                    <p className="text-sm text-secondary-600">Automatically capture, qualify, and route leads to your sales team.</p>
                  </div>
                  <div className="border-l-4 border-primary-600 pl-4">
                    <h5 className="font-semibold text-secondary-900">Customer Onboarding</h5>
                    <p className="text-sm text-secondary-600">Streamline new customer setup and welcome processes.</p>
                  </div>
                  <div className="border-l-4 border-primary-600 pl-4">
                    <h5 className="font-semibold text-secondary-900">Reporting & Analytics</h5>
                    <p className="text-sm text-secondary-600">Generate automated reports and insights from your data.</p>
                  </div>
                  <div className="border-l-4 border-primary-600 pl-4">
                    <h5 className="font-semibold text-secondary-900">Inventory Management</h5>
                    <p className="text-sm text-secondary-600">Automate stock tracking, reordering, and supplier communications.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-primary-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-primary-100 max-w-2xl mx-auto leading-relaxed mb-8">
            Let's discuss your project requirements and create a solution that drives results.
          </p>
          <a 
            href="/contact" 
            className="btn-secondary bg-white text-primary-600 hover:bg-primary-50"
          >
            Start Your Project
          </a>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;
