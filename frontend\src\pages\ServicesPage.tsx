import React from 'react';
import { GlassCard, GlassButton, GlassSection, SEOHead } from '../components/atoms';

const ServicesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* SEO Optimization */}
      <SEOHead
        title="Our Services - Full-Stack Development & Automation"
        description="Comprehensive full-stack web development and business automation services. Frontend, backend, DevOps, and intelligent workflow automation solutions."
        keywords="full-stack development services, frontend development, backend development, DevOps, business automation, React development, API development, cloud services"
        url="https://aptibot.com/services"
        type="website"
      />

      {/* Glassmorphism Hero Section */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl sm:text-6xl md:text-7xl font-heading font-bold mb-6 sm:mb-8 text-white leading-tight">
              Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Services</span>
            </h1>
            <p className="text-xl sm:text-2xl md:text-3xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              Comprehensive full-stack solutions designed to transform your business
              and accelerate your digital growth.
            </p>
          </div>
        </div>
      </GlassSection>

      {/* Full-Stack Web Development with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Full-Stack <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">Web Development</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                End-to-end web solutions from concept to deployment, built with modern
                technologies and best practices.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: "💻",
                  title: "Frontend Development",
                  description: "Modern, responsive user interfaces built with React, TypeScript, and cutting-edge frameworks.",
                  features: ["React & TypeScript", "Responsive Design", "Performance Optimization", "Accessibility Compliance"],
                  variant: "primary" as const,
                  gradient: "from-blue-500/20 to-cyan-500/20"
                },
                {
                  icon: "⚙️",
                  title: "Backend Development",
                  description: "Robust server-side solutions with APIs, databases, and cloud infrastructure.",
                  features: ["RESTful APIs", "Database Design", "Cloud Infrastructure", "Security Implementation"],
                  variant: "secondary" as const,
                  gradient: "from-purple-500/20 to-pink-500/20"
                },
                {
                  icon: "🚀",
                  title: "DevOps & Deployment",
                  description: "Streamlined deployment pipelines and infrastructure management for scalable applications.",
                  features: ["CI/CD Pipelines", "Cloud Deployment", "Monitoring & Analytics", "Performance Optimization"],
                  variant: "accent" as const,
                  gradient: "from-emerald-500/20 to-teal-500/20"
                }
              ].map((service, index) => (
                <GlassCard
                  key={index}
                  variant={service.variant}
                  size="lg"
                  hover
                  glow
                  className="group"
                >
                  <div className="text-center">
                    <div className="text-4xl mb-6 transform group-hover:scale-110 transition-transform duration-500">
                      {service.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-gray-300 mb-6 leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                      {service.description}
                    </p>
                    <ul className="text-sm text-gray-400 space-y-2 text-left">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Automation Services with Glassmorphism */}
      <GlassSection variant="gradient" padding="xl" particles>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
                Business Process <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-cyan-400">Automation</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Streamline your operations with intelligent automation solutions that save time,
                reduce errors, and boost productivity.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <GlassCard variant="accent" size="lg" hover glow>
                <h3 className="text-2xl font-semibold text-white mb-6">
                  Transform Your Workflow
                </h3>
                <div className="space-y-6">
                  {[
                    {
                      icon: "🔗",
                      title: "Data Integration",
                      description: "Connect disparate systems and automate data flow between applications."
                    },
                    {
                      icon: "⚡",
                      title: "Workflow Automation",
                      description: "Automate repetitive tasks and create efficient business processes."
                    },
                    {
                      icon: "🎯",
                      title: "Custom Solutions",
                      description: "Tailored automation solutions designed specifically for your business needs."
                    }
                  ].map((item, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="text-2xl">{item.icon}</div>
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-2">{item.title}</h4>
                        <p className="text-gray-300">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>

              <GlassCard variant="primary" size="lg" hover glow>
                <h4 className="text-xl font-semibold text-white mb-6">Popular Automation Use Cases</h4>
                <div className="space-y-4">
                  {[
                    {
                      title: "Lead Management",
                      description: "Automatically capture, qualify, and route leads to your sales team."
                    },
                    {
                      title: "Customer Onboarding",
                      description: "Streamline new customer setup and welcome processes."
                    },
                    {
                      title: "Reporting & Analytics",
                      description: "Generate automated reports and insights from your data."
                    },
                    {
                      title: "Inventory Management",
                      description: "Automate stock tracking, reordering, and supplier communications."
                    }
                  ].map((useCase, index) => (
                    <div key={index} className="border-l-4 border-cyan-400 pl-4 py-2">
                      <h5 className="font-semibold text-white">{useCase.title}</h5>
                      <p className="text-sm text-gray-300">{useCase.description}</p>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </div>
          </div>
        </div>
      </GlassSection>

      {/* Call to Action with Glassmorphism */}
      <GlassSection variant="dark" padding="xl">
        <div className="container mx-auto px-4 text-center">
          <GlassCard variant="primary" size="xl" hover glow className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-6">
              Ready to Get <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-teal-400">Started?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8">
              Let's discuss your project requirements and create a solution that drives results.
            </p>
            <GlassButton
              variant="accent"
              size="lg"
              onClick={() => window.location.href = '/contact'}
              className="transform hover:scale-110 transition-transform duration-300"
            >
              Start Your Project
            </GlassButton>
          </GlassCard>
        </div>
      </GlassSection>
    </div>
  );
};

export default ServicesPage;
