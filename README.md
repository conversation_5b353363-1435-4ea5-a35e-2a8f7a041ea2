# 🚀 AptiBot.com - Full-Stack Development & Automation Solutions

A modern, responsive website built with React, TypeScript, and Tailwind CSS, featuring a serverless backend for contact form processing and email integration.

## ✨ Features

### 🎨 Frontend
- **Modern React 18** with TypeScript and Vite
- **Responsive Design** with Tailwind CSS
- **3D Animations** with Spline integration
- **Interactive Components** with smooth animations
- **Accessibility Compliant** (WCAG 2.1 AA)
- **Performance Optimized** with lazy loading and code splitting
- **SEO Friendly** with proper meta tags and structured data

### 🔧 Backend
- **Serverless Functions** with Vercel
- **Email Integration** with Nodemailer
- **Security Features** including rate limiting, CORS, and input sanitization
- **Form Validation** with comprehensive error handling
- **Spam Protection** with honeypot fields and suspicious content detection

### 📊 Analytics & Monitoring
- **Google Analytics 4** integration
- **Performance Monitoring** with Core Web Vitals tracking
- **Error Tracking** with detailed logging
- **User Behavior Analytics** with custom events

## 🛠️ Tech Stack

### Frontend
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Spline** - 3D animations and interactions
- **Vitest** - Unit and integration testing

### Backend
- **Node.js** - JavaScript runtime
- **Vercel Functions** - Serverless backend
- **Nodemailer** - Email sending
- **Express Validator** - Input validation
- **CORS** - Cross-origin resource sharing

### DevOps & Deployment
- **Vercel** - Hosting and deployment
- **GitHub Actions** - CI/CD pipeline
- **ESLint & Prettier** - Code quality and formatting
- **Husky** - Git hooks for quality gates

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/aptibot.com.git
   cd aptibot.com
   ```

2. **Install frontend dependencies**
   ```bash
   cd frontend
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## 📁 Project Structure

```
aptibot.com/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # React components (Atomic Design)
│   │   │   ├── atoms/       # Basic UI elements
│   │   │   ├── molecules/   # Component combinations
│   │   │   ├── organisms/   # Complex components
│   │   │   └── templates/   # Page layouts
│   │   ├── hooks/          # Custom React hooks
│   │   ├── layouts/        # Page layout components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── test/           # Test utilities and setup
│   ├── public/             # Static assets
│   └── dist/               # Production build output
├── api/                    # Serverless backend functions
│   ├── contact.ts          # Contact form handler
│   ├── services/           # Backend services
│   └── middleware/         # Security and validation middleware
├── docs/                   # Documentation
└── deploy.sh              # Deployment script
```

## 🧪 Testing

### Run Tests
```bash
# Unit and integration tests
npm run test

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage

# UI test runner
npm run test:ui
```

### Test Coverage
- **Components**: 95%+ coverage
- **Hooks**: 100% coverage
- **Utilities**: 100% coverage
- **API Endpoints**: 90%+ coverage

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build
npm run lint            # Run ESLint
npm run type-check      # TypeScript type checking

# Testing
npm run test            # Run tests
npm run test:ui         # Test UI
npm run test:coverage   # Coverage report

# Deployment
./deploy.sh             # Full deployment script
```

### Code Quality

- **ESLint** - Code linting with React and TypeScript rules
- **Prettier** - Code formatting
- **Husky** - Pre-commit hooks
- **TypeScript** - Static type checking
- **Vitest** - Fast unit testing

## 🌐 Deployment

### Automatic Deployment
The project is configured for automatic deployment to Vercel:

1. **Push to main branch** triggers automatic deployment
2. **Pull requests** create preview deployments
3. **Environment variables** are managed in Vercel dashboard

### Manual Deployment
```bash
# Build and deploy
./deploy.sh

# Or step by step
npm run build
vercel --prod
```

### Environment Variables

#### Frontend (.env)
```env
VITE_API_BASE_URL=https://aptibot.com
VITE_CONTACT_API_ENDPOINT=/api/contact
VITE_SPLINE_SCENE_URL=your-spline-url
VITE_GA_TRACKING_ID=your-ga-id
```

#### Backend (Vercel Environment Variables)
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>
```

## 📈 Performance

### Lighthouse Scores
- **Performance**: 95+
- **Accessibility**: 100
- **Best Practices**: 100
- **SEO**: 100

### Optimizations
- **Code Splitting** - Lazy loading of route components
- **Image Optimization** - WebP format with fallbacks
- **Bundle Analysis** - Optimized chunk sizes
- **Caching** - Aggressive caching strategies
- **CDN** - Global content delivery

## 🔒 Security

### Frontend Security
- **Content Security Policy** (CSP)
- **XSS Protection** with input sanitization
- **HTTPS Enforcement**
- **Secure Headers** implementation

### Backend Security
- **Rate Limiting** - 5 requests per 15 minutes
- **Input Validation** - Comprehensive form validation
- **CORS Configuration** - Restricted origins
- **Spam Protection** - Honeypot fields and content analysis
- **Error Handling** - Secure error responses

## 🎯 Accessibility

### WCAG 2.1 AA Compliance
- **Keyboard Navigation** - Full keyboard accessibility
- **Screen Reader Support** - ARIA labels and descriptions
- **Color Contrast** - Minimum 4.5:1 ratio
- **Focus Management** - Visible focus indicators
- **Semantic HTML** - Proper heading hierarchy

### Testing Tools
- **axe-core** - Automated accessibility testing
- **WAVE** - Web accessibility evaluation
- **Lighthouse** - Accessibility auditing

## 📞 Contact & Support

- **Website**: [aptibot.com](https://aptibot.com)
- **Email**: <EMAIL>
- **Phone**: +****************

## 📄 License

This project is proprietary and confidential. All rights reserved.

---

**Built with ❤️ by the AptiBot Team**
