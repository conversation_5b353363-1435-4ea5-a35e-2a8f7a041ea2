{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.5.0", "@react-three/fiber": "^9.2.0", "@splinetool/react-spline": "^4.0.0", "@types/nodemailer": "^6.4.17", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.178.1", "axios": "^1.10.0", "cors": "^2.8.5", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "nodemailer": "^7.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-router-dom": "^7.6.3", "three": "^0.178.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4"}}