@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

/* Import custom glassmorphism styles */
@import './styles/glassmorphism.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white text-secondary-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-900 font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .heading-xl {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold font-heading leading-tight;
  }

  .heading-lg {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold font-heading leading-tight;
  }

  .heading-md {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold font-heading leading-tight;
  }

  .text-body {
    @apply text-base md:text-lg leading-relaxed;
  }

  /* Accessibility styles */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }

  .skip-link {
    @apply absolute -top-10 left-2 bg-black text-white px-2 py-1 text-sm rounded z-50 transition-all duration-300;
  }

  .skip-link:focus {
    @apply top-2;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced focus styles */
*:focus {
  outline: 2px solid #0284c7;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    border: 2px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Custom utilities */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Mobile-first responsive utilities */
.mobile-padding {
  @apply px-4 sm:px-6 md:px-8 lg:px-12;
}

.mobile-margin {
  @apply mx-4 sm:mx-6 md:mx-8 lg:mx-auto;
}

.mobile-text {
  @apply text-sm sm:text-base md:text-lg;
}

.mobile-heading {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
}

.mobile-button {
  @apply w-full sm:w-auto px-6 py-3 sm:px-8 sm:py-4;
}

/* Touch-friendly interactive elements */
.touch-target {
  @apply min-h-[44px] min-w-[44px];
}

/* Responsive grid utilities */
.grid-responsive {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8;
}

.grid-responsive-2 {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 lg:gap-12;
}

/* Safe area utilities for mobile devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Responsive visibility utilities */
.mobile-only {
  @apply block sm:hidden;
}

.tablet-only {
  @apply hidden sm:block lg:hidden;
}

.desktop-only {
  @apply hidden lg:block;
}

.mobile-hidden {
  @apply hidden sm:block;
}

/* Glassmorphism utilities */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(25px) saturate(200%);
  -webkit-backdrop-filter: blur(25px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px) saturate(150%);
  -webkit-backdrop-filter: blur(15px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Performance optimizations for mobile */
@media (max-width: 768px) {
  .reduce-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .mobile-optimized {
    will-change: auto;
    transform: translateZ(0);
  }

  /* Reduce glassmorphism complexity on mobile for performance */
  .glass, .glass-strong, .glass-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Mobile-specific hero section adjustments */
  .hero-content-mobile {
    padding: 2rem 1rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
}

/* Dark theme optimizations for the new animation */
.dark-hero-section {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
}

/* Ensure proper text contrast on dark backgrounds */
.text-shadow-glow {
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

/* Animation performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .hero-parallax {
    transform: none !important;
  }
}
